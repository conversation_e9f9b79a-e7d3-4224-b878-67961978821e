#!/usr/bin/env python3
"""
测试双重stroke渲染功能：单独stroke + 累积stroke
"""

import json
import os
import sys
from DrawingRenderer import DrawingRenderer

def create_progressive_test_data():
    """创建渐进式的测试数据，便于观察累积效果"""
    test_data = {
        "capture_info": {
            "timestamp": "2024-07-23T19:00:00.000000",
            "total_strokes": 4,
            "total_lines": 16,
            "total_bitmaps": 0,
            "stroke_ids": [1, 2, 3, 4]
        },
        "strokes": [
            {
                "stroke_id": 1,
                "start_timestamp": 1690123456789,
                "end_timestamp": 1690123457000,
                "type": "stroke",
                "lines": [
                    {
                        "point_id": 1,
                        "start_x": 400.0,
                        "start_y": 400.0,
                        "stop_x": 600.0,
                        "stop_y": 400.0,
                        "stroke_width": 8.0,
                        "color": -16777216,  # 黑色
                        "alpha": 255,
                        "timestamp": 1690123456789
                    },
                    {
                        "point_id": 2,
                        "start_x": 600.0,
                        "start_y": 400.0,
                        "stop_x": 600.0,
                        "stop_y": 600.0,
                        "stroke_width": 8.0,
                        "color": -16777216,
                        "alpha": 255,
                        "timestamp": 1690123456800
                    }
                ],
                "paths": []
            },
            {
                "stroke_id": 2,
                "start_timestamp": 1690123458000,
                "end_timestamp": 1690123459000,
                "type": "stroke",
                "lines": [
                    {
                        "point_id": 1,
                        "start_x": 600.0,
                        "start_y": 600.0,
                        "stop_x": 400.0,
                        "stop_y": 600.0,
                        "stroke_width": 8.0,
                        "color": -65536,  # 红色
                        "alpha": 255,
                        "timestamp": 1690123458000
                    },
                    {
                        "point_id": 2,
                        "start_x": 400.0,
                        "start_y": 600.0,
                        "stop_x": 400.0,
                        "stop_y": 400.0,
                        "stroke_width": 8.0,
                        "color": -65536,
                        "alpha": 255,
                        "timestamp": 1690123458100
                    }
                ],
                "paths": []
            },
            {
                "stroke_id": 3,
                "start_timestamp": 1690123460000,
                "end_timestamp": 1690123461000,
                "type": "stroke",
                "lines": [
                    {
                        "point_id": 1,
                        "start_x": 450.0,
                        "start_y": 450.0,
                        "stop_x": 550.0,
                        "stop_y": 450.0,
                        "stroke_width": 5.0,
                        "color": -16711936,  # 绿色
                        "alpha": 255,
                        "timestamp": 1690123460000
                    },
                    {
                        "point_id": 2,
                        "start_x": 550.0,
                        "start_y": 450.0,
                        "stop_x": 550.0,
                        "stop_y": 550.0,
                        "stroke_width": 5.0,
                        "color": -16711936,
                        "alpha": 255,
                        "timestamp": 1690123460100
                    },
                    {
                        "point_id": 3,
                        "start_x": 550.0,
                        "start_y": 550.0,
                        "stop_x": 450.0,
                        "stop_y": 550.0,
                        "stroke_width": 5.0,
                        "color": -16711936,
                        "alpha": 255,
                        "timestamp": 1690123460200
                    },
                    {
                        "point_id": 4,
                        "start_x": 450.0,
                        "start_y": 550.0,
                        "stop_x": 450.0,
                        "stop_y": 450.0,
                        "stroke_width": 5.0,
                        "color": -16711936,
                        "alpha": 255,
                        "timestamp": 1690123460300
                    }
                ],
                "paths": []
            },
            {
                "stroke_id": 4,
                "start_timestamp": 1690123462000,
                "end_timestamp": 1690123463000,
                "type": "stroke",
                "lines": [
                    {
                        "point_id": 1,
                        "start_x": 480.0,
                        "start_y": 480.0,
                        "stop_x": 520.0,
                        "stop_y": 480.0,
                        "stroke_width": 3.0,
                        "color": -256,  # 黄色
                        "alpha": 255,
                        "timestamp": 1690123462000
                    },
                    {
                        "point_id": 2,
                        "start_x": 520.0,
                        "start_y": 480.0,
                        "stop_x": 520.0,
                        "stop_y": 520.0,
                        "stroke_width": 3.0,
                        "color": -256,
                        "alpha": 255,
                        "timestamp": 1690123462100
                    },
                    {
                        "point_id": 3,
                        "start_x": 520.0,
                        "start_y": 520.0,
                        "stop_x": 480.0,
                        "stop_y": 520.0,
                        "stroke_width": 3.0,
                        "color": -256,
                        "alpha": 255,
                        "timestamp": 1690123462200
                    },
                    {
                        "point_id": 4,
                        "start_x": 480.0,
                        "start_y": 520.0,
                        "stop_x": 480.0,
                        "stop_y": 480.0,
                        "stroke_width": 3.0,
                        "color": -256,
                        "alpha": 255,
                        "timestamp": 1690123462300
                    }
                ],
                "paths": []
            }
        ]
    }
    return test_data

def test_dual_stroke_rendering():
    """测试双重stroke渲染功能"""
    print("Testing dual stroke rendering (single + cumulative)...")
    
    # 创建测试数据
    test_data = create_progressive_test_data()
    test_file = "test_dual_strokes.json"
    
    # 保存测试数据
    with open(test_file, 'w') as f:
        json.dump(test_data, f, indent=2)
    
    print(f"Created test data file: {test_file}")
    print(f"Test data contains {test_data['capture_info']['total_strokes']} strokes")
    print("Expected pattern: nested rectangles (black -> red -> green -> yellow)")
    
    # 创建渲染器
    renderer = DrawingRenderer()
    
    # 1. 测试双重单独渲染
    print("\n1. Testing dual individual stroke rendering...")
    individual_dir = "test_dual_individual"
    individual_files = renderer.render_individual_strokes(test_file, individual_dir)
    
    # 验证文件结构
    single_dir = os.path.join(individual_dir, "single")
    cumulative_dir = os.path.join(individual_dir, "cumulative")
    
    print(f"✓ Individual stroke rendering: {len(individual_files)} files")
    print(f"  - Single directory: {single_dir}")
    print(f"  - Cumulative directory: {cumulative_dir}")
    
    # 检查文件
    single_files = [f for f in individual_files if "single" in f]
    cumulative_files = [f for f in individual_files if "cumulative" in f]
    
    print(f"  - Single stroke files: {len(single_files)}")
    print(f"  - Cumulative stroke files: {len(cumulative_files)}")
    
    for file in individual_files[:8]:  # 只显示前8个文件
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"    ✓ {os.path.basename(file)} ({size} bytes)")
    
    # 2. 测试网格分析图（both模式）
    print("\n2. Testing dual grid analysis...")
    grid_both = "test_dual_grid_both.png"
    renderer.create_stroke_analysis_grid(test_file, grid_both, "both")
    print(f"✓ Dual grid analysis: {grid_both}")
    
    # 3. 测试单一类型网格
    print("\n3. Testing single-type grids...")
    grid_single = "test_dual_grid_single.png"
    grid_cumulative = "test_dual_grid_cumulative.png"
    
    renderer.create_stroke_analysis_grid(test_file, grid_single, "single")
    renderer.create_stroke_analysis_grid(test_file, grid_cumulative, "cumulative")
    
    print(f"✓ Single stroke grid: {grid_single}")
    print(f"✓ Cumulative stroke grid: {grid_cumulative}")
    
    # 4. 验证所有文件
    print("\n4. Verifying all output files...")
    all_files = individual_files + [grid_both, grid_single, grid_cumulative]
    
    success_count = 0
    for file in all_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            success_count += 1
        else:
            print(f"✗ Missing: {file}")
    
    print(f"✓ Successfully generated {success_count}/{len(all_files)} files")
    
    # 5. 分析累积效果
    print("\n5. Analyzing cumulative effect...")
    print("Expected cumulative progression:")
    print("  - Stroke 1: Black rectangle outline")
    print("  - Stroke 2: + Red rectangle outline (complete square)")
    print("  - Stroke 3: + Green inner rectangle")
    print("  - Stroke 4: + Yellow innermost rectangle")
    
    # 清理测试文件
    cleanup_files = [test_file, grid_both, grid_single, grid_cumulative]
    if os.path.exists(individual_dir):
        import shutil
        shutil.rmtree(individual_dir)
        print(f"Cleaned up directory: {individual_dir}")
    
    for file in cleanup_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"Cleaned up: {file}")
    
    return success_count == len(all_files)

def test_command_line_interface():
    """测试命令行接口的新功能"""
    print("\nTesting enhanced command line interface...")
    
    # 创建测试数据
    test_data = create_progressive_test_data()
    test_file = "test_cli_dual.json"
    
    with open(test_file, 'w') as f:
        json.dump(test_data, f, indent=2)
    
    import subprocess
    
    try:
        # 测试individual参数（现在生成双重图像）
        print("Testing --individual flag (dual rendering)...")
        result = subprocess.run([
            'python3', 'DrawingRenderer.py', test_file, 
            '--individual', '--output-dir', 'test_cli_dual_individual'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✓ --individual flag works (dual rendering)")
        else:
            print(f"✗ --individual flag failed: {result.stderr}")
        
        # 测试不同的grid类型
        for grid_type in ['single', 'cumulative', 'both']:
            print(f"Testing --grid with --grid-type {grid_type}...")
            result = subprocess.run([
                'python3', 'DrawingRenderer.py', test_file, 
                '--grid', '--grid-type', grid_type, 
                '-o', f'test_cli_grid_{grid_type}.png'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"✓ --grid-type {grid_type} works")
            else:
                print(f"✗ --grid-type {grid_type} failed: {result.stderr}")
                
    except subprocess.TimeoutExpired:
        print("✗ Command line test timed out")
        return False
    except Exception as e:
        print(f"✗ Command line test error: {e}")
        return False
    finally:
        # 清理
        cleanup_files = [test_file] + [f'test_cli_grid_{t}.png' for t in ['single', 'cumulative', 'both']]
        for file in cleanup_files:
            if os.path.exists(file):
                os.remove(file)
        
        if os.path.exists('test_cli_dual_individual'):
            import shutil
            shutil.rmtree('test_cli_dual_individual')
    
    return True

def main():
    """主测试函数"""
    print("=" * 70)
    print("Dual Stroke Rendering Test (Single + Cumulative)")
    print("=" * 70)
    
    try:
        # 测试核心功能
        if not test_dual_stroke_rendering():
            return 1
        
        # 测试命令行接口
        if not test_command_line_interface():
            return 1
        
        print("\n" + "=" * 70)
        print("All tests completed successfully!")
        print("Dual stroke rendering (single + cumulative) is working properly.")
        print("=" * 70)
        
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
