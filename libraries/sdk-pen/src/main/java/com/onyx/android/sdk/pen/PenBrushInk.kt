package com.onyx.android.sdk.pen

/**
 * deep seek analyze instance size is 24Byte:
 * PenBrushInk object internals:
 * OFF  SZ      TYPE DESCRIPTION               VALUE
 *   0   8          (object header: mark)      N/A
 *   8   4          (object header: class)     N/A
 *  12   4     float PenBrushInk.x             N/A
 *  16   4     float PenBrushInk.y             N/A
 *  20   1   UByte  PenBrushInk.size           N/A
 *  21   1   UByte  PenBrushInk.angle36        N/A
 *  22   1   UByte  PenBrushInk.alpha          N/A
 *  23   1          (alignment/padding gap)
 * Instance size: 24 bytes
 *
 *
 * profile shadow_size is 19Byte
 */
class PenBrushInk(
    var x: Float, var y: Float, var size: UByte, var angle36: UByte, var alpha: UByte
) {

    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (other == null) {
            return false
        }
        if (other !is PenBrushInk) {
            return false
        }

        return x == other.x && y == other.y && size == other.size
                && angle36 == other.angle36 && alpha == other.alpha
    }

}