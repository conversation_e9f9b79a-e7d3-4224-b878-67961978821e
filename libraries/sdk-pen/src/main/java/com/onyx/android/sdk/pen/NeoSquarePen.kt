package com.onyx.android.sdk.pen

import com.onyx.android.sdk.pen.utils.PenUtils

class NeoSquarePen private constructor(
    handle: Long): NeoNativePen(handle) {

    companion object {
        fun defaultPenConfig() = NeoPenConfig().also {
            it.directionEnabled = true
            it.brushShape = NeoPenConfig.NEOPEN_BRUSH_SHAPE_ELLIPSE
            it.smoothLevel = PenUtils.DEFAULT_SMOOTH_LEVEL
        }

        fun create(config: NeoPenConfig): NeoPen? {
            val handle = NeoPenNative.createPen(NeoPenConfig.NEOPEN_PEN_TYPE_SQUARE, config)
            if (handle == 0.toLong()) {
                return null
            }
            return NeoSquarePen(handle)
        }
    }

    override fun buildPenResult(result: NeoPenResult?): Pair<PenResult?, PenResult?> {
        return Pair(
            PenPathResult.buildFromInkArray(result?.realInk),
            PenPathResult.buildFromInkArray(result?.predictionInk)
        )
    }
}