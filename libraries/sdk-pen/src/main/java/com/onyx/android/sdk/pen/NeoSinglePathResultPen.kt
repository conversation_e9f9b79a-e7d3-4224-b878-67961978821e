package com.onyx.android.sdk.pen

import android.graphics.Path
import android.graphics.RectF
import com.onyx.android.sdk.base.data.TouchPoint
import com.onyx.android.sdk.base.lite.extension.getQuadToEndPos

/**
 * <pre>
 *     author : suicheng
 *     time   : 2024/4/13 10:01
 *     desc   :
 * </pre>
 */
open class NeoSinglePathResultPen(penHandle: Long = 0) : NeoPen(penHandle) {
    protected var path = Path()
    private var firstPoint = TouchPoint()
    private var lastPoint = TouchPoint()
    private var endPoint = TouchPoint()

    override fun onPenDown(
        point: TouchPoint,
        prediction: TouchPoint?
    ): Pair<PenResult?, PenResult?> {
        return onPenDown(point, false)
    }

    override fun onPenDown(point: TouchPoint, repaint: Boolean): Pair<PenResult?, PenResult?> {
        firstPoint = point
        lastPoint.set(point)
        endPoint.set(point)
        path = Path()
        path.moveTo(point.x, point.y)
        return Pair(null, null)
    }

    override fun onPenMove(
        points: List<TouchPoint>,
        prediction: TouchPoint?
    ): Pair<PenResult?, PenResult?> {
        return onPenMove(points, prediction, false)
    }

    override fun onPenMove(
        points: List<TouchPoint>,
        prediction: TouchPoint?,
        repaint: Boolean
    ): Pair<PenResult?, PenResult?> {
        if (points.isEmpty()) {
            return Pair(null, null)
        }

        val realResult = composeRealPenResult(points)
        val predictionResult = composePredictPenResult(prediction)

        val compositePath = Path()
        compositePath.addPath(realResult.path)
        predictionResult?.also {
            compositePath.addPath(it.path)
            realResult.rect.union(it.rect)
        }
        val compositeResult = PenPathResult(compositePath, realResult.rect)

        return Pair(compositeResult, predictionResult)
    }

    override fun onPenUp(point: TouchPoint, prediction: TouchPoint?): Pair<PenResult?, PenResult?> {
        return onPenUp(point, false)
    }

    override fun onPenUp(point: TouchPoint, repaint: Boolean): Pair<PenResult?, PenResult?> {
        val penResult = composeRealPenResult(listOf(point))
        return Pair(penResult, null)
    }

    private fun composeRealPenResult(points: List<TouchPoint>): PenPathResult {
        val rect = RectF(firstPoint.x, firstPoint.y, firstPoint.x, firstPoint.y)
        for (point in points) {
            val endX = getQuadToEndPos(lastPoint.x, point.x)
            val endY = getQuadToEndPos(lastPoint.y, point.y)
            path.quadTo(lastPoint.x, lastPoint.y, endX, endY)
            endPoint.x = endX
            endPoint.y = endY
            lastPoint.set(point)
            rect.union(endX, endY)
        }
        return PenPathResult(path, rect)
    }

    private fun composePredictPenResult(predictPoint: TouchPoint?): PenPathResult? {
        if (predictPoint == null) {
            return null
        }
        val predictPath = Path().apply {
            moveTo(endPoint.x, endPoint.y)
            quadTo(lastPoint.x, lastPoint.y, predictPoint.x, predictPoint.y)
        }
        val predictRect = RectF(endPoint.x, endPoint.y, predictPoint.x, predictPoint.y)
        return PenPathResult(predictPath, predictRect)
    }
}