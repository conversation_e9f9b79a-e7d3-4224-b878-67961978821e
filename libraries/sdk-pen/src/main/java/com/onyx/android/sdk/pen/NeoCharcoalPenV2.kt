package com.onyx.android.sdk.pen

import com.onyx.android.sdk.pen.utils.PenUtils

class NeoCharcoalPenV2 private constructor(penHandle: Long) : NeoNativePen(penHandle) {

    companion object {
        fun defaultPenConfig() = NeoPenConfig().also {
            it.type = NeoPenConfig.NEOPEN_PEN_TYPE_CHARCOAL_V2
            it.tiltScale = PenUtils.DEFAULT_TILT_SCALE
        }

        fun create(config: NeoPenConfig): NeoCharcoalPenV2? {
            val handle = NeoPenNative.createPen(NeoPenConfig.NEOPEN_PEN_TYPE_CHARCOAL_V2, config)
            if (handle == 0.toLong()) {
                return null
            }
            return NeoCharcoalPenV2(handle)
        }
    }

    override fun buildPenResult(result: NeoPenResult?): Pair<PenResult?, PenResult?> {
        return Pair(
                PenTextureResult.buildFromInkArray(result?.realInk),
                PenTextureResult.buildFromInkArray(result?.predictionInk)
        )
    }
}