package com.onyx.android.sdk.pen

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.graphics.RectF
import com.onyx.android.sdk.base.lite.extension.copy
import com.onyx.android.sdk.base.utils.Debug

class PenPathResult(
    internal val path: Path,
    rect: RectF
) : PenResult(rect) {
    var points = floatArrayOf()
    var pointSizeArray: IntArray = intArrayOf()

    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (other == null || other !is PenPathResult) {
            return false
        }
        if (points.size != other.points.size) {
            return false
        }
        for (i in points.indices) {
            if (points[i] != other.points[i]) {
                return false
            }
        }
        return true
    }

    override fun clearCache() {
        points = EMPTY_FLOAT_ARRAY
        pointSizeArray = EMPTY_INT_ARRAY
    }

    override fun append(add: PenResult?): PenResult {
        if (add == null) {
            return this
        }

        val addPath = add as PenPathResult

        val newPath = path.copy()
        newPath.addPath(addPath.path)

        val newRect = rect.copy()
        newRect.union(addPath.rect)

        return PenPathResult(newPath, newRect)
    }

    override fun draw(canvas: Canvas, paint: Paint) {
        canvas.drawPath(path, paint)
    }

    companion object {
        private val EMPTY_FLOAT_ARRAY = floatArrayOf()
        private val EMPTY_INT_ARRAY = intArrayOf()
        private val DUMP_POINTS = false
        private var pathCount = 0
        private var pointCount = 0

        fun resetCount() {
            pathCount = 0
            pointCount = 0
        }

        fun dumpCount() {
            Debug.i(javaClass, "path count: $pathCount, point count: $pointCount")
        }

        fun buildFromInkArray(ink: PenInk?): PenPathResult? {
            if (ink == null) {
                return null
            }

            var path = Path()
            var rect: RectF? = null

            var start = 0
            var end = 0
            val pointsArrayList = mutableListOf<FloatArray>()

            path.incReserve(ink.points.size / 2)

            for (i in 0 until ink.pointSizeArray.size) {
                if (DUMP_POINTS) {
                    pathCount++
                }

                end += ink.pointSizeArray[i]

                val res = buildFromInk(path!!, ink.points, start, end)
                pointsArrayList.add(res.third)

                if (rect == null) {
                    rect = RectF(res.second)
                }
                rect.union(res.second)

                start = end
            }

            if (rect == null) {
                return null
            }

            return PenPathResult(path, rect).apply {
                this.points = ink.points
                this.pointSizeArray = ink.pointSizeArray
            }
        }

        private fun buildFromInk(
            path: Path,
            points: FloatArray,
            start: Int,
            end: Int
        ): Triple<Path, RectF, FloatArray> {
            var rect = RectF()
            val arrayList = mutableListOf<Float>()
            for (i in start until end step 2) {
                if (DUMP_POINTS) {
                    pointCount++
                }

                val x = points[i]
                val y = points[i + 1]
                if (i == start) {
                    path.moveTo(x, y)
                    rect = RectF(x, y, x, y)
                } else {
                    path.lineTo(x, y)
                    rect.union(x, y)
                }
                arrayList.add(x)
                arrayList.add(y)
            }
            path.close()

            return Triple(path, rect, arrayList.toFloatArray())
        }
    }
}