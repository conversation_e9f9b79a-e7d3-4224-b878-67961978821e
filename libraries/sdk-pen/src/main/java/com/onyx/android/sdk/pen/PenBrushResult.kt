package com.onyx.android.sdk.pen

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.RectF
import android.os.Build
import androidx.annotation.RequiresApi
import com.onyx.android.sdk.base.lite.utils.ColorUtils
import com.onyx.android.sdk.pen.utils.PenUtils.scaleTranslate
import kotlin.math.max

@RequiresApi(Build.VERSION_CODES.Q)
class PenBrushResult(
    private val brushPoints: List<PenBrushInk>,
    private val maskGenerator: NeoPencilPen.Companion.BrushMaskGenerator,
    rect: RectF
) : PenResult(rect) {

    var matrix = Matrix()
    var pointSizeScale = 1.0f
    var isEnabledClipRect = false

    private val canvasBoundingRectF = RectF()
    private val invertMatrix = Matrix()

    private val pointScaleRenderMatrix = Matrix()
    private val pointBitmapRenderMatrix = Matrix()
    private val tempMaskKey = NeoPencilPen.Companion.MaskKey(MIN_POINT_SIZE, 0)

    override fun append(add: PenResult?): PenResult? {
        return null
    }

    override fun draw(canvas: Canvas, paint: Paint) {
        updateMatrixWithPointSizeScale()
        normalizeCanvasBoundingRectF(canvas)
        val shapeAlpha = paint.alpha
        val shapeColor = paint.color
        for (point in brushPoints) {
            if (isEnabledDrawMask(point)) {
                drawMask(canvas, shapeColor, shapeAlpha, point)
            }
        }
    }

    private fun isEnabledDrawMask(point: PenBrushInk): Boolean {
        if (!isEnabledClipRect) {
            return true
        }
        return canvasBoundingRectF.contains(point.x, point.y)
    }

    private fun normalizeCanvasBoundingRectF(canvas: Canvas) {
        if (!isEnabledClipRect) {
            return
        }
        canvasBoundingRectF.set(0f, 0f, canvas.width.toFloat(), canvas.height.toFloat())
        val pointRenderMaxWidth = maskGenerator.config.width * pointSizeScale
        canvasBoundingRectF.inset(-pointRenderMaxWidth, -pointRenderMaxWidth)
        invertMatrix.reset()
        matrix.invert(invertMatrix)
        invertMatrix.mapRect(canvasBoundingRectF)
    }

    private fun updateMatrixWithPointSizeScale() {
        pointScaleRenderMatrix.apply {
            reset()
            set(matrix)
            postScale(1 / pointSizeScale, 1 / pointSizeScale)
            scaleTranslate(pointSizeScale, pointSizeScale)
        }
    }

    override fun equals(other: Any?): Boolean {
        if (other == null) {
            return false
        }
        if (other !is PenBrushResult) {
            return false
        }
        if (brushPoints.size != other.brushPoints.size) {
            return false
        }
        for (i in brushPoints.indices) {
            if (brushPoints[i] != other.brushPoints[i]) {
                return false
            }
        }

        return true
    }

    private fun drawMask(
        canvas: Canvas,
        shapeColor: Int,
        shapeAlpha: Int,
        point: PenBrushInk
    ) {
        val pointSizeInt = max(MIN_POINT_SIZE, (point.size.toFloat() / POINT_SIZE_FACTOR * maskGenerator.config.width * pointSizeScale).toInt())

        tempMaskKey.size = pointSizeInt
        tempMaskKey.angle = point.angle36.toInt()
        val maskBitmapHolder = maskGenerator.getMaskBitmap(tempMaskKey)
        val maskBitmap = maskBitmapHolder.bitmap
        val left = point.x * pointSizeScale - maskBitmapHolder.halfWidth
        val top = point.y * pointSizeScale - maskBitmapHolder.halfWidth

        val pointActualAlpha = (point.alpha.toFloat() / MAX_ALPHA_INT * shapeAlpha * maskGenerator.config.alphaFactor).toInt()
        val pointAlphaInt = pointActualAlpha.coerceIn(MIN_ALPHA_INT, MAX_ALPHA_INT)
        val paintHolder = bitmapPaintMap.computeIfAbsent(pointAlphaInt) { PaintHolder(shapeColor, pointAlphaInt) }
        if (shapeColor != paintHolder.shapeColor) {
            paintHolder.updatePaintColor(shapeColor, pointAlphaInt)
        }

        maskBitmap.let {
            pointBitmapRenderMatrix.setTranslate(left, top)
            pointBitmapRenderMatrix.postConcat(pointScaleRenderMatrix)
            canvas.drawBitmap(it, pointBitmapRenderMatrix, paintHolder.paint)
        }
    }

    private class PaintHolder(
        var shapeColor: Int = Color.BLACK,
        alpha: Int
    ) {
        val paint = Paint().apply {
            this.isAntiAlias = false
            this.isDither = false
            this.isFilterBitmap = false
            color = ColorUtils.replaceAlphaWithBitwise(shapeColor, alpha)
        }

        fun updatePaintColor(shapeColor: Int, alpha: Int) {
            this.shapeColor = shapeColor
            paint.color = ColorUtils.replaceAlphaWithBitwise(shapeColor, alpha)
        }
    }

    companion object {
        const val POINT_SIZE_FACTOR = 255f
        private const val MAX_ALPHA_INT = 255
        private const val MIN_ALPHA_INT = 4
        private const val MIN_POINT_SIZE = 2
        private val bitmapPaintMap = mutableMapOf<Int, PaintHolder>()

        fun PenBrushResult?.getPointsCount(): Int {
            return this?.brushPoints?.size ?: 0
        }
    }
}
