package com.onyx.android.sdk.pen

import java.io.Serializable

class NeoRenderPoint : Serializable {
    var x = 0f
    var y = 0f
    var size = 0f
    var bitmapIndex = 0

    companion object {

        @JvmStatic
        fun create(
            x: Float,
            y: Float,
            size: Float,
            bitmapIndex: Int
        ): NeoRenderPoint {
            val point = NeoRenderPoint()
            point.x = x
            point.y = y
            point.size = size
            point.bitmapIndex = bitmapIndex
            return point
        }

        @JvmStatic
        fun create(p: NeoRenderPoint): NeoRenderPoint {
            return create(p.x, p.y, p.size, p.bitmapIndex)
        }
    }
}