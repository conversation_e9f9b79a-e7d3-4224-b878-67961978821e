package com.onyx.android.sdk.pen

import android.annotation.TargetApi
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.RectF
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.annotation.WorkerThread
import com.onyx.android.sdk.base.lite.extension.createScaledBitmap
import com.onyx.android.sdk.base.lite.utils.MathUtils
import com.onyx.android.sdk.base.utils.Debug
import com.onyx.android.sdk.base.utils.ResManager
import com.onyx.android.sdk.pen.PenBrushResult.Companion.POINT_SIZE_FACTOR
import com.onyx.android.sdk.pen.utils.PenUtils
import java.util.concurrent.ConcurrentHashMap

@RequiresApi(Build.VERSION_CODES.Q)
class NeoPencilPen private constructor(
    penHandle: Long,
    val maskGenerator: BrushMaskGenerator
) : NeoNativePen(penHandle) {

    companion object {
        fun defaultPenConfig() = NeoPenConfig().also {
            it.type = NeoPenConfig.NEOPEN_PEN_TYPE_PENCIL
            it.minWidth = 1.0f
            it.brushSpacing = PenUtils.DEFAULT_BRUSH_SPACING
            it.pressureSensitivity = PenUtils.DEFAULT_PRESSURE_SENSITIVITY
            it.smoothLevel = PenUtils.DEFAULT_PENCIL_SMOOTH_LEVEL
        }

        /**
         * @angle range: [0, 35]
         */
        data class MaskKey(var size: Int, var angle: Int)

        data class BitmapHolder(val bitmap: Bitmap, val halfWidth: Float)

        private val maskBitmaps = ConcurrentHashMap<MaskKey, BitmapHolder>()
        private val rotatedBitmaps = ConcurrentHashMap<Int, Bitmap>()

        class BrushMaskGenerator(val config: NeoPenConfig) {

            /**
             * multiple thread access safe
             */
            fun getMaskBitmap(key: MaskKey): BitmapHolder {
                val bitmapHolder = maskBitmaps[key]
                if (bitmapHolder != null) {
                    return bitmapHolder
                }

                val newKey = key.copy()
                return maskBitmaps.computeIfAbsent(newKey) {
                    // TODO use first brush shape, can be improve by use all brush shapes
                    val bitmap = getRotatedBitmap(newKey.angle)
                        .createScaledBitmap(newKey.size, newKey.size)
                        .copy(Bitmap.Config.ALPHA_8, true)
                    BitmapHolder(bitmap,  bitmap.width / 2f)
                }
            }
        }

        @Synchronized
        private fun prepareRotatedBitmaps() {
            val count = 36
            if (rotatedBitmaps.size >= count) {
                return
            }
            val startTime = System.currentTimeMillis()
            val srcBitmap = decodePencilOriginBitmap()
            for (i in 0 until count) {
                rotatedBitmaps.computeIfAbsent(i) {
                    srcBitmap.rotate(it * 10f)
                }
            }
            val benchmark = System.currentTimeMillis() - startTime
            Debug.d(javaClass, "prepareRotatedBitmaps --> ${benchmark}ms")
        }

        private fun decodePencilOriginBitmap():Bitmap {
            return BitmapFactory.decodeResource(
                ResManager.getAppContext().resources,
                R.drawable.pencil
            )
        }

        private fun getRotatedBitmap(angle: Int): Bitmap {
            val srcBitmap = rotatedBitmaps.computeIfAbsent(0) {
                decodePencilOriginBitmap()
            }
            return rotatedBitmaps.computeIfAbsent(angle) {
                srcBitmap.rotate(angle * 10f)
            }
        }

        private fun Bitmap.rotate(angle: Float): Bitmap {
            val srcBitmap = this
            val matrix = Matrix()
            val px = srcBitmap.width / 2f
            val py = srcBitmap.height / 2f
            matrix.postRotate(angle, px, py)
            val config = srcBitmap.config ?: Bitmap.Config.ARGB_8888
            val bitmap = Bitmap.createBitmap(srcBitmap.width, srcBitmap.height, config)
            val canvas = Canvas(bitmap)
            canvas.drawBitmap(srcBitmap, matrix,null)
            return bitmap
        }

        fun create(config: NeoPenConfig): NeoPencilPen? {
            val handle = NeoPenNative.createPen(NeoPenConfig.NEOPEN_PEN_TYPE_PENCIL, config)
            if (handle == 0.toLong()) {
                return null
            }
            return NeoPencilPen(handle, BrushMaskGenerator(config))
        }

        @WorkerThread
        @TargetApi(Build.VERSION_CODES.Q)
        fun prepareBrushMask(config: NeoPenConfig):BrushMaskGenerator {
            prepareRotatedBitmaps()
            return BrushMaskGenerator(config)
        }
    }

    override fun buildPenResult(result: NeoPenResult?): Pair<PenResult?, PenResult?> {
        return Pair(
            buildFromInkArray(result?.realInk),
            buildFromInkArray(result?.predictionInk)
        )
    }

    private fun buildFromInkArray(ink: PenInk?): PenBrushResult? {
        if (ink == null) {
            return null
        }

        val brushPoints = ArrayList<PenBrushInk>()
        var addedRect: RectF? = null

        for (i in 0 until ink.points.size step  5) {
            val x = ink.points[i]
            val y = ink.points[i + 1]
            val size = ink.points[i + 2]
            val rotation = ink.points[i + 3]
            val alpha = ((ink.points[i + 4]) * 255).toInt().coerceIn(0, 255).toUByte()
            val angle36 = MathUtils.normalizeAngleTo36(Math.toDegrees(rotation.toDouble()))

            val sizeByte = (size / maskGenerator.config.width * POINT_SIZE_FACTOR)
                .coerceIn(0f, POINT_SIZE_FACTOR)
                .toInt().toUByte()
            brushPoints.add(PenBrushInk(x, y, sizeByte, angle36, alpha))

            if (addedRect == null) {
                addedRect = RectF(x, y, x + size, y + size)
            }
            addedRect.union(x, y)
            addedRect.union(x + size, y + size)
        }

        if (addedRect == null) {
            return null
        }

        return PenBrushResult(brushPoints, maskGenerator, addedRect)
    }
}