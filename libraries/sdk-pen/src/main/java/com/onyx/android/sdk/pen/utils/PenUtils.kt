package com.onyx.android.sdk.pen.utils

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Rect
import com.onyx.android.sdk.base.data.TouchPoint
import com.onyx.android.sdk.bean.DefaultPenParams
import com.onyx.android.sdk.pen.NeoRenderPoint
import kotlin.math.max
import kotlin.math.min

/**
 * <pre>
 * author : liao lin tao
 * time   : 2018/12/5 14:32
 * desc   :
</pre> *
 */
object PenUtils {
    const val DEFAULT_PARAMS_CONFIG_RAW_NAME = "pen_default_params_config"
    const val FLOAT_ONE = 1.0f

    private val ERASE_EXTRA_STROKE_WIDTH: Float = FLOAT_ONE
    private var penBitmap: Bitmap? = null

    const val DEFAULT_MIN_WIDTH = 0.05f
    const val DEFAULT_TILT_SCALE = 3f
    const val DEFAULT_BRUSH_SPACING = 0.25f
    const val MAX_PRESSURE = 1f
    const val MIN_PRESSURE = 0.001f
    const val FINGER_DEFAULT_PRESSURE = FLOAT_ONE * 0.7f
    const val FINGER_DOWN_UP_PRESSURE = FINGER_DEFAULT_PRESSURE * 0.15f
    const val MIN_PRESSURE_SENSITIVITY = 0.15f
    const val MAX_PRESSURE_SENSITIVITY = 0.6f
    private const val RANGE_PRESSURE_SENSITIVITY = MAX_PRESSURE_SENSITIVITY - MIN_PRESSURE_SENSITIVITY
    const val PRESSURE_SENSITIVITY_LEVEL = 10
    const val DEFAULT_PRESSURE_SENSITIVITY = 0.375f
    val DEFAULT_VELOCITY_SENSITIVITY get() = defaultPenParams.defaultVelocitySensitivity
    val DEFAULT_VELOCITY_AMPLIFIER get() = defaultPenParams.defaultVelocityAmplifier
    val DEFAULT_VELOCITY_IGNORE_THRESHOLD get() = defaultPenParams.defaultVelocityIgnoreThreshold
    val DEFAULT_VELOCITY_LOWER_BOUND get() = defaultPenParams.defaultVelocityLowerBound
    val DEFAULT_VELOCITY_UPPER_BOUND get() = defaultPenParams.defaultVelocityUpperBound
    const val DEFAULT_ALPHA_FACTOR = 1.0f
    const val MIN_ALPHA_FACTOR = 0.05f
    const val MAX_ALPHA_FACTOR = 2.0f
    const val DEFAULT_DPI = 320f
    const val MIN_PRECISION = FLOAT_ONE
    const val MAX_PRECISION = 8f
    const val MIDDLE_PRECISION = MAX_PRECISION / 2
    const val DEFAULT_SMOOTH_LEVEL = 0.2f
    const val DEFAULT_PENCIL_SMOOTH_LEVEL = 0.2f

    private var defaultPenParams: DefaultPenParams = DefaultPenParams()

    fun initDefaultPenParams(defaultPenParams: DefaultPenParams) {
        this.defaultPenParams = defaultPenParams
    }

    fun ensurePenBitmapCreated(drawRect: Rect): Bitmap {
        if (penBitmap == null || penBitmap!!.width != drawRect.width() || penBitmap!!.height != drawRect.height()) {
            penBitmap = Bitmap.createBitmap(drawRect.width(), drawRect.height(), Bitmap.Config.ARGB_8888)
        }
        penBitmap?.eraseColor(Color.TRANSPARENT)
        return penBitmap as Bitmap
    }

    fun drawStrokeByPointSize(
        canvas: Canvas,
        paint: Paint,
        points: List<TouchPoint>,
        erase: Boolean
    ) {
        val p = Paint(paint).apply {
            style = Paint.Style.FILL_AND_STROKE
            strokeCap = Paint.Cap.ROUND
            strokeJoin = Paint.Join.ROUND
            isAntiAlias = true
        }
        for (i in 0 until points.size - 1) {
            var paintWidth: Float = points[i + 1].size
            if (erase) {
                paintWidth += ERASE_EXTRA_STROKE_WIDTH
            }
            p.strokeWidth = paintWidth
            canvas.drawLine(points[i].x, points[i].y, points[i + 1].x, points[i + 1].y, p)
        }
    }

    fun toTouchPoints(points: Array<NeoRenderPoint>): ArrayList<TouchPoint> {
        val touchPoints: ArrayList<TouchPoint> = ArrayList()
        for (p in points) {
            touchPoints.add(TouchPoint(p.x, p.y, 0f, p.size, 0))
        }
        return touchPoints
    }

    fun getPointArray(points: List<TouchPoint>, maxTouchPressure: Float): FloatArray {
        val array = FloatArray(points.size * 5)
        for (i in points.indices) {
            val idx = 5 * i
            array[idx] = points[i].x
            array[idx + 1] = points[i].y
            array[idx + 2] = points[i].pressure / maxTouchPressure
            array[idx + 3] = points[i].size
            array[idx + 4] = points[i].timestamp.toFloat()
        }
        return array
    }

    fun getPointDoubleArrayNullable(point: TouchPoint?, maxTouchPressure: Float = FLOAT_ONE): DoubleArray? {
        if (point == null) {
            return null
        }
        return getPointDoubleArray(point, maxTouchPressure)
    }

    fun getPointDoubleArray(point: TouchPoint, maxTouchPressure: Float = FLOAT_ONE): DoubleArray {
        val array = DoubleArray(7)
        array[0] = point.x.toDouble()
        array[1] = point.y.toDouble()
        array[2] = (point.pressure / maxTouchPressure).toDouble()
        array[3] = point.size.toDouble()
        array[4] = point.tiltX.toDouble()
        array[5] = point.tiltY.toDouble()
        array[6] = point.timestamp.toDouble()
        return array
    }

    fun getPointDoubleArray(points: List<TouchPoint>, maxTouchPressure: Float = FLOAT_ONE): DoubleArray {
        val array = DoubleArray(points.size * 7)
        for (i in points.indices) {
            val idx = 7 * i
            array[idx] = points[i].x.toDouble()
            array[idx + 1] = points[i].y.toDouble()
            array[idx + 2] = min(FLOAT_ONE, points[i].pressure / maxTouchPressure).toDouble()
            array[idx + 3] = points[i].size.toDouble()
            array[idx + 4] = points[i].tiltX.toDouble()
            array[idx + 5] = points[i].tiltY.toDouble()
            array[idx + 6] = points[i].timestamp.toDouble()
        }
        return array
    }

    fun toNormalizedPressureSensitivity(percentValue: Float): Float {
        return min(MAX_PRESSURE_SENSITIVITY, MIN_PRESSURE_SENSITIVITY + RANGE_PRESSURE_SENSITIVITY * percentValue)
    }

    fun toPercentPressureSensitivity(normalizedValue: Float): Float {
        return max(normalizedValue - MIN_PRESSURE_SENSITIVITY, 0f) / (RANGE_PRESSURE_SENSITIVITY)
    }

    fun Matrix?.scaleTranslate(scaleX: Float, scaleY: Float): Matrix? {
        this ?: return null
        val matrixValue = getMatrixValue()
        val transX = matrixValue.getMatrixTransX()
        val transY = matrixValue.getMatrixTransY()
        postTranslate(-transX, -transY)
        postTranslate(transX * scaleX, transY * scaleY)
        return this
    }

    fun Matrix?.getTransX(): Float {
        if (this == null) {
            return 0f
        }
        return getMatrixValue().getMatrixTransX()
    }

    fun Matrix?.getTransY(): Float {
        if (this == null) {
            return 0f
        }
        return getMatrixValue().getMatrixTransY()
    }

    fun Matrix?.getMatrixValue(): FloatArray {
        val values = FloatArray(9)
        this?.getValues(values)
        return values
    }

    fun FloatArray.getMatrixTransX(): Float {
        return this[Matrix.MTRANS_X]
    }

    fun FloatArray.getMatrixTransY(): Float {
        return this[Matrix.MTRANS_Y]
    }
}