package com.onyx.android.sdk.pen

import com.onyx.android.sdk.base.data.TouchPoint
import com.onyx.android.sdk.base.utils.Debug
import com.onyx.android.sdk.pen.utils.PenUtils

object NeoPenNative {
    init {
        System.loadLibrary("neo_pen")

        val logLevelDebug = 1
        val logLevelInfo = 2
        nativeSetLogLevel(logLevelInfo)
    }

    private val DUMP_POINTS = false

    private external fun nativeSetLogLevel(level: Int)

    private external fun nativeCreatePen(penType: Int, config: NeoPenConfig?): Long
    private external fun nativeDestroyPen(pen: Long)
    private external fun nativeOnPenDown(
        pen: Long,
        point: DoubleArray,
        repaint: Boolean
    ): NeoPenResult

    private external fun nativeOnPenMove(
        pen: Long,
        point: DoubleArray,
        prediction: DoubleArray?,
        repaint: Boolean
    ): NeoPenResult

    private external fun nativeOnPenUp(
        pen: Long,
        point: DoubleArray,
        repaint: Boolean
    ): NeoPenResult

    fun createPen(penType: Int, config: NeoPenConfig): Long {
        return nativeCreatePen(penType, config)
    }

    fun destroyPen(pen: Long) {
        nativeDestroyPen(pen)
    }

    fun onPenDown(
        pen: Long,
        point: TouchPoint,
        repaint: Boolean
    ): NeoPenResult {
        val result = nativeOnPenDown(
            pen,
            PenUtils.getPointDoubleArray(point),
            repaint
        )

        if (DUMP_POINTS) {
            Debug.i(javaClass, "onPenDown: $point")
            dumpResult(result)
        }

        return result
    }

    fun onPenMove(
        pen: Long,
        points: List<TouchPoint>,
        prediction: TouchPoint?,
        repaint: Boolean
    ): NeoPenResult {
        val result = nativeOnPenMove(
            pen,
            PenUtils.getPointDoubleArray(points),
            PenUtils.getPointDoubleArrayNullable(prediction),
            repaint
        )

        if (DUMP_POINTS) {
            Debug.i(
                javaClass, "onPenMove: " + points.first().toString() +
                        ", prediction: " + prediction.toString()
            )
            dumpResult(result)
        }

        return result
    }

    fun onPenUp(
        pen: Long,
        point: TouchPoint,
        repaint: Boolean
    ): NeoPenResult {
        val result = nativeOnPenUp(
            pen,
            PenUtils.getPointDoubleArray(point),
            repaint
        )

        if (DUMP_POINTS) {
            Debug.i(javaClass, "onPenUp: $point")
            dumpResult(result)
        }

        return result
    }

    private fun dumpResult(result: NeoPenResult) {
        dumpResultPrettify("real ink", result.realInk.toString())
        dumpResultPrettify("prediction ink", result.predictionInk.toString())
    }

    private fun dumpResultPrettify(title: String, message: String) {
        var msg = message
        while (msg.length > 80) {
            var shortMsg = msg.subSequence(0, 80)
            Debug.i(javaClass, "$title: $shortMsg")
            msg = msg.substring(80)
        }
        Debug.i(javaClass, "$title: $msg")
    }
}