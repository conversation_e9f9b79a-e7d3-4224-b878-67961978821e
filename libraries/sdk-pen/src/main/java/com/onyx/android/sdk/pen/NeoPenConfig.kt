package com.onyx.android.sdk.pen

import android.graphics.Bitmap
import android.graphics.Color
import com.onyx.android.sdk.pen.utils.PenUtils
import com.onyx.android.sdk.pen.utils.PenUtils.FLOAT_ONE
import com.onyx.android.sdk.pen.utils.PenUtils.MIDDLE_PRECISION
import com.onyx.android.sdk.pen.utils.PenUtils.MIN_PRECISION
import java.io.Serializable

class NeoPenConfig : Serializable {
    var type = 0
    var fastMode = false
    var color = Color.BLACK
    var width = 3.0f
    var minWidth = 0f
    var rotateAngle = 0
    var tiltEnabled = false
    var tiltScale = 0f
    var directionEnabled = false
    var maxTouchPressure = FLOAT_ONE
    var dpi = PenUtils.DEFAULT_DPI
    var displayScaleX = FLOAT_ONE
    var displayScaleY = FLOAT_ONE
    var scalePrecision = FLOAT_ONE
    // range from [0.1, 1]
    var brushSpacing = PenUtils.DEFAULT_BRUSH_SPACING
    var brushShape = NEOPEN_BRUSH_SHAPE_CIRCLE
    var brushRatio = 5.0f
    var brushAngle = 0.0f
    // range from [0, 1]
    var pressureSensitivity = 0f
    // range from [0, 1]
    var velocitySensitivity = 0f
    // range from [0, 1]
    var velocityAmplifier = 0f
    // range from [0, velocityLowerBound)
    var velocityIgnoreThreshold = 0f
    // range from [0, 50]
    var velocityLowerBound = 0f
    // range from [0, 50]
    var velocityUpperBound = 0f
    // range from [0, 1]
    var smoothLevel = PenUtils.DEFAULT_SMOOTH_LEVEL

    var alphaFactor = PenUtils.DEFAULT_ALPHA_FACTOR
    var brushShapes: MutableList<Bitmap>? = null

    fun setType(type: Int): NeoPenConfig {
        this.type = type
        return this
    }

    fun setDpi(dpi: Float): NeoPenConfig {
        this.dpi = dpi
        return this
    }

    fun setDisplayScaleX(scale: Float): NeoPenConfig {
        this.displayScaleX = scale
        return this
    }

    fun setDisplayScaleY(scale: Float): NeoPenConfig {
        this.displayScaleY = scale
        return this
    }

    fun setColor(color: Int): NeoPenConfig {
        this.color = color
        return this
    }

    fun setWidth(width: Float): NeoPenConfig {
        this.width = width
        return this
    }

    fun setRotateAngle(rotateAngle: Int): NeoPenConfig {
        this.rotateAngle = rotateAngle
        return this
    }

    fun setTiltEnabled(tiltEnabled: Boolean): NeoPenConfig {
        this.tiltEnabled = tiltEnabled
        return this
    }

    fun setTiltScale(tiltScale: Float): NeoPenConfig {
        this.tiltScale = tiltScale
        return this
    }

    fun setMaxTouchPressure(maxTouchPressure: Float): NeoPenConfig {
        this.maxTouchPressure = maxTouchPressure
        return this
    }

    fun setBrushSpacing(spacing: Float): NeoPenConfig {
        this.brushSpacing = spacing
        return this
    }

    fun resetBrushRatioAndAngle() {
        brushRatio = 5.0f
        brushAngle = 0f
    }

    companion object {
        const val NEOPEN_PEN_TYPE_BRUSH = 1
        const val NEOPEN_PEN_TYPE_FOUNTAIN = 2
        const val NEOPEN_PEN_TYPE_MARKER = 3
        const val NEOPEN_PEN_TYPE_CHARCOAL = 4
        const val NEOPEN_PEN_TYPE_CHARCOAL_V2 = 5
        const val NEOPEN_PEN_TYPE_FOUNTAIN_V2 = 6
        const val NEOPEN_PEN_TYPE_PENCIL = 7
        const val NEOPEN_PEN_TYPE_BALLPOINT = 8
        const val NEOPEN_PEN_TYPE_SQUARE = 9
        const val NEOPEN_PEN_TYPE_XIULI = 10

        const val NEOPEN_BRUSH_SHAPE_CIRCLE = 0
        const val NEOPEN_BRUSH_SHAPE_ELLIPSE = 1
        const val NEOPEN_BRUSH_SHAPE_RECTANGLE = 2

        fun getPrecision(scale: Float): Float {
            if (scale < 2) {
                return MIN_PRECISION
            }
            if (scale < 8) {
                return MIDDLE_PRECISION
            }
            return PenUtils.MAX_PRECISION
        }
    }
}