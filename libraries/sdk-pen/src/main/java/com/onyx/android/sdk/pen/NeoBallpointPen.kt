package com.onyx.android.sdk.pen

import com.onyx.android.sdk.pen.utils.PenUtils

object NeoBallpointPen {

    fun defaultPenConfig() = NeoPenConfig().also {
        it.type = NeoPenConfig.NEOPEN_PEN_TYPE_BALLPOINT
        it.smoothLevel = PenUtils.DEFAULT_SMOOTH_LEVEL
    }

    fun create(segment: Boolean = true): NeoPen {
        return if (segment) {
            NeoSegmentPathResultPen()
        } else {
            NeoSinglePathResultPen()
        }
    }
}