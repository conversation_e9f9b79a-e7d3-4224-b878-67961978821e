package com.onyx.android.sdk.pen

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF

abstract class PenResult(val rect: RectF) {
    abstract fun append(add: PenResult?): PenResult?
    abstract fun draw(canvas: Canvas, paint: Paint)
    open fun clearCache() {}

    companion object {

        fun List<Pair<PenResult?, PenResult?>>?.clearCache() {
            this?.forEach { it.first?.clearCache() }
        }

        fun List<Pair<PenResult?, PenResult?>>?.render(canvas: Canvas, paint: Paint) {
            this ?: return
            forEach {
                it.first.render(canvas, paint)
            }
            lastOrNull()?.second?.render(canvas, paint)
        }

        fun PenResult?.render(canvas: Canvas, paint: Paint) {
            this ?: return
            draw(canvas, paint)
        }
    }
}