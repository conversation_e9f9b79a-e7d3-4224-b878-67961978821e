package com.onyx.android.sdk.pen

import com.onyx.android.sdk.base.data.TouchPoint
import com.onyx.android.sdk.pen.utils.PenUtils

class NeoFountainPen private constructor(
    handle: Long, internal var config: NeoPenConfig): NeoNativePen(handle) {

    companion object {
        fun defaultPenConfig() = NeoPenConfig().also {
            it.type = NeoPenConfig.NEOPEN_PEN_TYPE_FOUNTAIN_V2
            it.minWidth = PenUtils.DEFAULT_MIN_WIDTH
            it.pressureSensitivity = PenUtils.DEFAULT_PRESSURE_SENSITIVITY
            it.velocitySensitivity = PenUtils.DEFAULT_VELOCITY_SENSITIVITY
            it.velocityAmplifier = PenUtils.DEFAULT_VELOCITY_AMPLIFIER
            it.velocityIgnoreThreshold = PenUtils.DEFAULT_VELOCITY_IGNORE_THRESHOLD
            it.velocityLowerBound = PenUtils.DEFAULT_VELOCITY_LOWER_BOUND
            it.velocityUpperBound = PenUtils.DEFAULT_VELOCITY_UPPER_BOUND
            it.smoothLevel = PenUtils.DEFAULT_SMOOTH_LEVEL
        }

        fun create(config: NeoPenConfig): NeoPen? {
            val handle = NeoPenNative.createPen(NeoPenConfig.NEOPEN_PEN_TYPE_FOUNTAIN_V2, config)
            if (handle == 0.toLong()) {
                return null
            }
            return NeoFountainPen(handle, config)
        }
    }

    private var lastPointResult: PenPointResult? = null

    override fun onPenDown(point: TouchPoint, repaint: Boolean): Pair<PenResult?, PenResult?> {
        lastPointResult = null
        return super.onPenDown(point, repaint)
    }

    override fun onPenDown(
        point: TouchPoint,
        prediction: TouchPoint?
    ): Pair<PenResult?, PenResult?> {
        lastPointResult = null
        return super.onPenDown(point, prediction)
    }

    override fun buildPenResult(result: NeoPenResult?): Pair<PenResult?, PenResult?> {
        if (config.fastMode) {
            val realResult = PenPointResult.buildFromInkArray(result?.realInk, lastPointResult)
            val predictionResult = PenPointResult.buildFromInkArray(result?.predictionInk, realResult)
            if (realResult != null) {
                lastPointResult = realResult
            }

            return Pair(realResult, predictionResult)
        }

        return Pair(
            PenPathResult.buildFromInkArray(result?.realInk),
            PenPathResult.buildFromInkArray(result?.predictionInk)
        )
    }
}