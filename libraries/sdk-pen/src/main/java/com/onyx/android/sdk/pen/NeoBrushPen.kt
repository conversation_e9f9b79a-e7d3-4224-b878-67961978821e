package com.onyx.android.sdk.pen

import com.onyx.android.sdk.base.data.TouchPoint

class NeoBrushPen private constructor(
    handle: Long): NeoNativePen(handle) {

    private var lastResult: PenPointResult? = null

    override fun onPenDown(point: TouchPoint, repaint: Boolean): Pair<PenResult?, PenResult?> {
        lastResult = null
        return super.onPenDown(point, repaint)
    }

    companion object {
        fun create(config: NeoPenConfig): NeoPen? {
            val handle = NeoPenNative.createPen(NeoPenConfig.NEOPEN_PEN_TYPE_BRUSH, config)
            if (handle == 0.toLong()) {
                return null
            }
            return NeoBrushPen(handle)
        }
    }

    override fun buildPenResult(result: NeoPenResult?): Pair<PenResult?, PenResult?> {
        val realResult = PenPointResult.buildFromInkArray(result?.realInk, lastResult)
        val predictionResult = PenPointResult.buildFromInkArray(result?.predictionInk, realResult)
        if (realResult != null) {
            lastResult = realResult
        }

        return Pair(realResult, predictionResult)
    }
}