package com.onyx.android.sdk.pen

import com.onyx.android.sdk.base.data.TouchPoint

abstract class NeoPen(internal val penHandle: Long) {
    fun destroy() {
        NeoPenNative.destroyPen(penHandle)
    }

    @Deprecated("")
    abstract fun onPenDown(point: TouchPoint, prediction: TouchPoint?):
            Pair<PenResult?, PenResult?>
    open fun onPenDown(point: TouchPoint, repaint: Boolean):
            Pair<PenResult?, PenResult?> {
        return Pair(null, null)
    }

    @Deprecated("")
    abstract fun onPenMove(points: List<TouchPoint>, prediction: TouchPoint?):
            Pair<PenResult?, PenResult?>
    open fun onPenMove(points: List<TouchPoint>, prediction: TouchPoint?, repaint: Boolean):
            Pair<PenResult?, PenResult?> {
        return Pair(null, null)
    }

    @Deprecated("")
    abstract fun onPenUp(point: TouchPoint, prediction: TouchPoint?):
            Pair<PenResult?, PenResult?>
    open fun onPenUp(point: TouchPoint, repaint: Boolean):
            Pair<PenResult?, PenResult?> {
        return Pair(null, null)
    }
}