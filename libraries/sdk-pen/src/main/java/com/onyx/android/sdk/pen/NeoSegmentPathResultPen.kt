package com.onyx.android.sdk.pen

import android.graphics.Path
import android.graphics.RectF
import com.onyx.android.sdk.base.data.TouchPoint
import com.onyx.android.sdk.base.lite.extension.getQuadToEndPos

class NeoSegmentPathResultPen(penHandle: Long = 0) : NeoPen(penHandle) {
    private var lastPoint = TouchPoint()
    private var endPoint = TouchPoint()

    override fun onPenDown(
        point: TouchPoint,
        prediction: TouchPoint?
    ): Pair<PenResult?, PenResult?> {
        return onPenDown(point, false)
    }

    override fun onPenDown(point: TouchPoint, repaint: Boolean): Pair<PenResult?, PenResult?> {
        lastPoint.set(point)
        endPoint.set(point)
        return Pair(null, null)
    }

    override fun onPenMove(
        points: List<TouchPoint>,
        prediction: TouchPoint?
    ): Pair<PenResult?, PenResult?> {
        return onPenMove(points, prediction, false)
    }

    override fun onPenMove(
        points: List<TouchPoint>,
        prediction: TouchPoint?,
        repaint: Boolean
    ): Pair<PenResult?, PenResult?> {
        if (points.isEmpty()) {
            return Pair(null, null)
        }

        var realRect: RectF? = null
        val path = Path()
        path.moveTo(endPoint.x, endPoint.y)

        for (point in points) {
            val pair = composeRealPath(path, point)
            if (realRect == null) {
                realRect = pair.second
            } else {
                realRect.union(pair.second)
            }
        }

        val realResult = realRect?.let {
            PenPathResult(path, it)
        }

        val predictionResult = composePredictPath(prediction)?.let {
            PenPathResult(it.first, it.second)
        }

        return Pair(realResult, predictionResult)
    }

    override fun onPenUp(point: TouchPoint, prediction: TouchPoint?): Pair<PenResult?, PenResult?> {
        return onPenUp(point, false)
    }

    override fun onPenUp(point: TouchPoint, repaint: Boolean): Pair<PenResult?, PenResult?> {
        val path = Path()
        path.moveTo(endPoint.x, endPoint.y)
        val pair = composeRealPath(path, point)
        return Pair(PenPathResult(pair.first, pair.second), null)
    }

    private fun composeRealPath(path: Path, point: TouchPoint): Pair<Path, RectF> {
        val endX = getQuadToEndPos(lastPoint.x, point.x)
        val endY = getQuadToEndPos(lastPoint.y, point.y)

        path.quadTo(lastPoint.x, lastPoint.y, endX, endY)

        val rect = RectF(endPoint.x, endPoint.y, endPoint.x, endPoint.y)
        rect.union(endX, endY)

        endPoint.x = endX
        endPoint.y = endY
        lastPoint.set(point)

        return Pair(path, rect)
    }

    private fun composePredictPath(point: TouchPoint?): Pair<Path, RectF>? {
        if (point == null) {
            return null
        }

        val path = Path()
        path.moveTo(endPoint.x, endPoint.y)
        path.quadTo(lastPoint.x, lastPoint.y, point.x, point.y)

        val rect = RectF(endPoint.x, endPoint.y, endPoint.x, endPoint.y)
        rect.union(point.x, point.y)

        return Pair(path, rect)
    }
}