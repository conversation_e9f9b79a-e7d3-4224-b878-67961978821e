package com.onyx.android.sdk.pen

class NeoCharcoalPen private constructor(penHandle: Long) : NeoNativePen(penHandle) {

    companion object {
        fun defaultPenConfig() = NeoPenConfig().also {
            it.type = NeoPenConfig.NEOPEN_PEN_TYPE_CHARCOAL
        }

        fun create(config: NeoPenConfig): NeoCharcoalPen? {
            val handle = NeoPenNative.createPen(NeoPenConfig.NEOPEN_PEN_TYPE_CHARCOAL, config)
            if (handle == 0.toLong()) {
                return null
            }
            return NeoCharcoalPen(handle)
        }
    }

    override fun buildPenResult(result: NeoPenResult?): Pair<PenResult?, PenResult?> {
        return Pair(
                PenTextureResult.buildFromInkArray(result?.realInk),
                PenTextureResult.buildFromInkArray(result?.predictionInk)
        )
    }
}