package com.onyx.android.sdk.pen

import com.onyx.android.sdk.base.data.TouchPoint

abstract class NeoNativePen(penHandle: Long) : NeoPen(penHandle) {

    override fun onPenDown(point: TouchPoint, prediction: TouchPoint?):
            Pair<PenResult?, PenResult?> {
        return buildPenResult(
            NeoPenNative.onPenDown(
                penHandle,
                point,
                false
            )
        )
    }

    override fun onPenDown(point: TouchPoint, repaint: Boolean): Pair<PenResult?, PenResult?> {
        return buildPenResult(
            NeoPenNative.onPenDown(
                penHandle,
                point,
                repaint
            )
        )
    }

    override fun onPenMove(points: List<TouchPoint>, prediction: TouchPoint?):
            Pair<PenResult?, PenResult?> {
        return buildPenResult(
            NeoPenNative.onPenMove(
                penHandle,
                points,
                prediction,
                false
            )
        )
    }

    override fun onPenMove(
        points: List<TouchPoint>,
        prediction: TouchPoint?,
        repaint: Boolean
    ): Pair<PenResult?, PenResult?> {
        return buildPenResult(
            NeoPenNative.onPenMove(
                penHandle,
                points,
                prediction,
                repaint
            )
        )
    }

    override fun onPenUp(point: TouchPoint, prediction: TouchPoint?):
            Pair<PenResult?, PenResult?> {
        return buildPenResult(
            NeoPenNative.onPenUp(
                penHandle,
                point,
                false
            )
        )
    }

    override fun onPenUp(point: TouchPoint, repaint: Boolean): Pair<PenResult?, PenResult?> {
        return buildPenResult(
            NeoPenNative.onPenUp(
                penHandle,
                point,
                repaint
            )
        )
    }

    internal open fun buildPenResult(result: NeoPenResult?): Pair<PenResult?, PenResult?> {
        return Pair(null, null)
    }
}