package com.onyx.android.sdk.pen

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import com.onyx.android.sdk.base.utils.Debug

class PenPointResult(val points: List<PenPointInk>, rect: RectF, private val lastResult: PenPointResult?)
    : PenResult(rect) {

    override fun append(add: PenResult?): PenResult? {
        if (add == null) {
            return this
        }

        val addResult = add as PenPointResult

        val newPoints = ArrayList<PenPointInk>(points)
        newPoints.addAll(addResult.points)

        val newRect = RectF(rect)
        newRect.union(addResult.rect)

        return PenPointResult(newPoints, newRect, lastResult)
    }

    override fun draw(canvas: Canvas, paint: Paint) {
        // TODO(joy): what about erase width?
        if (lastResult != null && points.isNotEmpty()) {
            paint.strokeWidth = points.first().size
            canvas.drawLine(lastResult.points.last().x, lastResult.points.last().y,
                points.first().x, points.first().y, paint)
        }

        for (i in 0 until points.size - 1) {
            paint.strokeWidth = points[i + 1].size
            canvas.drawLine(points[i].x, points[i].y, points[i + 1].x, points[i + 1].y, paint)
        }
    }

    companion object {
        private val DUMP_POINTS = true
        private var pointCount = 0

        fun resetCount() {
            pointCount = 0
        }

        fun dumpCount() {
            Debug.i(javaClass, "point count: $pointCount")
        }

        fun buildFromInkArray(ink: PenInk?, lastResult: PenPointResult?): PenPointResult? {
            if (ink == null) {
                return null
            }

            val points = ArrayList<PenPointInk>()
            var addedRect: RectF? = null

            for (i in 0 until ink.points.size step 3) {
                val x = ink.points[i]
                val y = ink.points[i + 1]
                val size = ink.points[i + 2]
                points.add(PenPointInk(x, y, size))
                if (DUMP_POINTS) {
                    pointCount++
                }

                if (addedRect == null) {
                    addedRect = RectF(x, y, x, y)
                } else {
                    addedRect.union(x, y)
                }
            }

            if (addedRect == null) {
                return null
            }

            return PenPointResult(points, addedRect, lastResult)
        }
    }
}