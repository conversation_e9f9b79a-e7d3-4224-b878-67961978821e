package com.onyx.android.sdk.pen

import android.graphics.Bitmap

class PenInk(val points: Float<PERSON>rray, val pointSizeArray: IntArray, val bitmaps: Array<Bitmap>) {

    companion object {
        @JvmStatic
        fun create(
            points: FloatArray,
            pointSizeArray: IntArray,
            bitmap: Array<Bitmap>
        ): PenInk {
            return PenInk(points, pointSizeArray, bitmap)
        }
    }

    override fun toString(): String {
        val builder = StringBuilder()
        builder.append("PenInk: ").append(points.size).append(" -> [")

        if (points.isNotEmpty()) {
            builder.append(points.first())
        }
        for (i in 1 until points.size) {
            builder.append(", ").append(points[i])
        }

        builder.append("]")
        return builder.toString()
    }
}