package com.onyx.android.sdk.pen

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF

class PenTextureResult(
    private val textures: List<PenTextureInk>,
    rect: RectF
) : PenResult(rect) {
    override fun append(add: PenResult?): PenResult? {
        return null
    }
    override fun draw(canvas: Canvas, paint: Paint) {
        for (texture in textures) {
            canvas.drawBitmap(texture.bitmap, texture.x, texture.y, paint)
        }
    }

    companion object {
        fun buildFromInkArray(ink: PenInk?): PenTextureResult? {
            if (ink == null) {
                return null
            }
            val textures = ArrayList<PenTextureInk>()
            var addedRect: RectF? = null

            for (i in 0 until ink.points.size step 2) {
                val bitmapIdx = i / 2
                if (bitmapIdx < ink.bitmaps.size) {
                    val bitmap = ink.bitmaps[bitmapIdx]

                    val x = ink.points[i]
                    val y = ink.points[i + 1]
                    textures.add(PenTextureInk(x, y, bitmap))

                    if (addedRect == null) {
                        addedRect = RectF(x, y, x + bitmap.width, y + bitmap.height)
                    }
                    addedRect.union(x, y)
                    addedRect.union(x + bitmap.width, y + bitmap.height)
                }
            }

            if (addedRect == null) {
                return null
            }

            return PenTextureResult(textures, addedRect)
        }
    }
}
