package com.onyx.android.sdk.base.data

import android.graphics.Matrix
import android.graphics.Path
import android.graphics.PathMeasure
import android.graphics.RectF
import android.view.MotionEvent
import com.onyx.android.sdk.base.lite.utils.MathUtils
import com.onyx.android.sdk.base.lite.utils.MathUtils.FLOAT_ONE
import com.onyx.android.sdk.base.lite.utils.MathUtils.distance
import com.onyx.android.sdk.base.lite.utils.MathUtils.isEmptyMatrix
import java.io.Serializable
import java.util.Objects
import kotlin.math.PI
import kotlin.math.abs
import kotlin.math.acos
import kotlin.math.cos
import kotlin.math.max
import kotlin.math.min
import kotlin.math.pow
import kotlin.math.round
import kotlin.math.sqrt
import kotlin.math.tan

/**
 * Created by zhuzeng on 4/22/16.
 */
class TouchPoint(
    var x: Float = 0f,
    var y: Float = 0f,
    var pressure: Float = 0f,
    var size: Float = 0f,
    var tiltX: Int = 0,
    var tiltY: Int = 0,
    var timestamp: Long = 0
) : Serializable, Cloneable {

    constructor(x: Float, y: Float) : this(
        x = x,
        y = y,
        timestamp = System.currentTimeMillis()
    )

    constructor(x: Float, y: Float, p: Float, s: Float, t: Long) : this(
        x = x,
        y = y,
        pressure = p,
        size = s,
        timestamp = t
    )

    constructor(motionEvent: MotionEvent) : this(
        x = motionEvent.x,
        y = motionEvent.y,
        pressure = motionEvent.pressure,
        size = motionEvent.size,
        timestamp = motionEvent.eventTime
    ) {
        val (tx, ty) = computeAndroidTiltXY(motionEvent.getAxisValue(MotionEvent.AXIS_TILT),
            motionEvent.getAxisValue(MotionEvent.AXIS_ORIENTATION))
        tiltX = tx
        tiltY = ty
    }

    constructor(source: TouchPoint) : this() {
        set(source)
    }

    fun set(x: Float, y: Float) {
        this.x = x
        this.y = y
    }

    fun set(point: TouchPoint) {
        x = point.x
        y = point.y
        pressure = point.pressure
        size = point.size
        tiltX = point.tiltX
        tiltY = point.tiltY
        timestamp = point.timestamp
    }

    fun offset(dx: Int, dy: Int) {
        offset(dx.toFloat(), dy.toFloat())
    }

    fun offset(dx: Float, dy: Float) {
        x += dx
        y += dy
    }

    // TODO: add doc page info depend
    /*fun normalize(pageInfo: PageInfo) {
        x = (x - pageInfo.getDisplayRect().left) / pageInfo.getActualScale()
        y = (y - pageInfo.getDisplayRect().top) / pageInfo.getActualScale()
    }

    fun origin(pageInfo: PageInfo) {
        x = x * pageInfo.getActualScale() + pageInfo.getDisplayRect().left
        y = y * pageInfo.getActualScale() + pageInfo.getDisplayRect().top
    }*/

    fun invertMatrix(matrix: Matrix?) {
        if (matrix == null) {
            return
        }
        if (isEmptyMatrix(matrix)) {
            return
        }
        val invertMatrix = Matrix()
        if (!matrix.invert(invertMatrix)) {
            return
        }
        applyMatrix(invertMatrix)
    }

    fun applyMatrix(matrix: Matrix?): TouchPoint {
        if (matrix == null) {
            return this
        }
        if (isEmptyMatrix(matrix)) {
            return this
        }
        val pts = floatArrayOf(x, y)
        matrix.mapPoints(pts)
        x = pts[0]
        y = pts[1]
        return this
    }

    fun transform(matrix: Matrix?): TouchPoint {
        val transformed = copy()
        transformed.applyMatrix(matrix)
        return transformed
    }

    fun concatTransform(vararg matrixs: Matrix?): TouchPoint {
        val matrix = Matrix()
        matrixs.forEach { matrix.postConcat(it ?: Matrix()) }
        return transform(matrix)
    }

    fun translate(dx: Float, dy: Float) {
        x += dx
        y += dy
    }

    fun setTimestamp(timestamp: Long): TouchPoint? {
        this.timestamp = timestamp
        return this
    }

    fun scale(scaleValue: Float): TouchPoint? {
        x = x * scaleValue
        y = y * scaleValue
        return this
    }

    fun scale(scaleX: Float, scaleY: Float): TouchPoint {
        x *= scaleX
        y *= scaleY
        return this
    }

    fun mapMatrix(matrix: Matrix?) {
        if (matrix == null) {
            return
        }
        val pts = floatArrayOf(x, y)
        matrix.mapPoints(pts)
        x = pts[0]
        y = pts[1]
    }

    fun copy(): TouchPoint = TouchPoint(this)

    public override fun clone(): TouchPoint {
        return super.clone() as TouchPoint
    }

    override fun toString(): String {
        return "x:$x y:$y pressure:$pressure size:$size"
    }

    override fun equals(o: Any?): Boolean {
        if (this === o) return true
        if (o == null || javaClass != o.javaClass) return false
        val point = o as TouchPoint
        return java.lang.Float.compare(point.x, x) == 0 && java.lang.Float.compare(
            point.y,
            y
        ) == 0 && java.lang.Float.compare(point.pressure, pressure) == 0 && java.lang.Float.compare(
            point.size,
            size
        ) == 0 && timestamp == point.timestamp
    }

    fun equalXY(o: TouchPoint): Boolean {
        return this.x == o.x && this.y == o.y
    }

    fun equalXYApproximately(other: TouchPoint, threshold: Number = FLOAT_ONE): Boolean {
        return distance(other) < threshold.toFloat()
    }

    override fun hashCode(): Int {
        return Objects.hash(x, y, pressure, size, timestamp)
    }

    fun isEmpty() = this == Empty

    // TODO, move to TouchPointUtils
    companion object {
        const val OBJECT_BYTE_COUNT = 32

        val Empty = TouchPoint()

        fun fromHistorical(event: MotionEvent): List<TouchPoint> {
            val size = event.historySize
            val list = mutableListOf<TouchPoint>()
            for (i in 0 until size) {
                list.add(fromHistorical(event, i))
            }
            list.add(TouchPoint(event))
            return list
        }

        fun fromHistorical(motionEvent: MotionEvent, i: Int): TouchPoint {
            return TouchPoint(
                motionEvent.getHistoricalX(i),
                motionEvent.getHistoricalY(i),
                motionEvent.getHistoricalPressure(i),
                motionEvent.getHistoricalSize(i),
                motionEvent.getHistoricalEventTime(i)
            )
        }

        fun renderPointArray(matrix: Matrix?, touchPoints: List<TouchPoint>): List<TouchPoint> {
            return touchPoints.map { it.copy().applyMatrix(matrix) }
        }

        fun getPointAngle(start: TouchPoint, end: TouchPoint): Float {
            val tan = Math.atan2((end.x - start.x).toDouble(), (start.y - end.y).toDouble())
            return (180 * tan / Math.PI).toFloat()
        }

        fun getHorizontalAngle(start: TouchPoint, end: TouchPoint): Float {
            //return LineUtils.getHorizontalAngle(start.x, start.y, end.x, end.y)
            return 0f
        }

        fun getPointDistance(x1: Float, y1: Float, x2: Float, y2: Float): Float {
            return sqrt(
                (x1 - x2).toDouble().pow(2.0) + (y1 - y2).toDouble().pow(2.0)
            ).toFloat()
        }

        // clockwise direction points start from left top
        fun getTransformRectPoints(originRect: RectF, matrix: Matrix?): FloatArray? {
            val size = 8
            val src = FloatArray(size)
            val dst = FloatArray(size)
            var lst = FloatArray(size)
            val result = FloatArray(size)
            var index = 0
            src[index++] = originRect.left
            src[index++] = originRect.top
            src[index++] = originRect.right
            src[index++] = originRect.top
            src[index++] = originRect.right
            src[index++] = originRect.bottom
            src[index++] = originRect.left
            src[index++] = originRect.bottom
            if (matrix == null) {
                return src
            }
            matrix.mapPoints(dst, src)
            matrix.mapRect(originRect)
            var myMatrix = Matrix()
            myMatrix.postRotate(90f, dst[0], dst[1])
            myMatrix.mapPoints(lst, dst)
            val intersectLeftBottom = getIntersection(
                TouchPoint(dst[0], dst[1]),
                TouchPoint(lst[2], lst[3]),
                TouchPoint(dst[4], dst[5]),
                TouchPoint(dst[6], dst[7])
            )
            myMatrix = Matrix()
            lst = FloatArray(size)
            myMatrix.postRotate(90f, dst[4], dst[5])
            myMatrix.mapPoints(lst, dst)
            val intersectRightTopPoint = getIntersection(
                TouchPoint(dst[4], dst[5]),
                TouchPoint(lst[6], lst[7]),
                TouchPoint(dst[0], dst[1]),
                TouchPoint(dst[2], dst[3])
            )
            if (!originRect.contains(
                    intersectLeftBottom.x,
                    intersectLeftBottom.y
                ) && !originRect.contains(
                    intersectRightTopPoint.x,
                    intersectRightTopPoint.y
                )
            ) {
                result[0] = dst[0]
                result[1] = dst[1]
                result[2] = intersectRightTopPoint.x
                result[3] = intersectRightTopPoint.y
                result[4] = dst[4]
                result[5] = dst[5]
                result[6] = intersectLeftBottom.x
                result[7] = intersectLeftBottom.y
                return result
            }
            myMatrix = Matrix()
            lst = FloatArray(size)
            myMatrix.postRotate(90f, dst[2], dst[3])
            myMatrix.mapPoints(lst, dst)
            val intersectLeftTopPoint = getIntersection(
                TouchPoint(lst[4], lst[5]),
                TouchPoint(dst[2], dst[3]),
                TouchPoint(dst[6], dst[7]),
                TouchPoint(dst[0], dst[1])
            )
            myMatrix = Matrix()
            lst = FloatArray(size)
            myMatrix.postRotate(90f, dst[6], dst[7])
            myMatrix.mapPoints(lst, dst)
            val intersectRightBottomPoint = getIntersection(
                TouchPoint(dst[6], dst[7]),
                TouchPoint(lst[0], lst[1]),
                TouchPoint(dst[2], dst[3]),
                TouchPoint(dst[4], dst[5])
            )
            result[0] = intersectLeftTopPoint.x
            result[1] = intersectLeftTopPoint.y
            result[2] = dst[2]
            result[3] = dst[3]
            result[4] = intersectRightBottomPoint.x
            result[5] = intersectRightBottomPoint.y
            result[6] = dst[6]
            result[7] = dst[7]
            return result
        }

        // get a b line and c d line intersection point
        fun getIntersection(
            a: TouchPoint,
            b: TouchPoint,
            c: TouchPoint,
            d: TouchPoint
        ): TouchPoint {
            val intersection = TouchPoint(a)
            if ((Math.abs(b.y - a.y) + Math.abs(b.x - a.x) + Math.abs(d.y - c.y)
                        + Math.abs(d.x - c.x)) == 0f
            ) {
                return intersection
            }
            if (Math.abs(b.y - a.y) + Math.abs(b.x - a.x) == 0f) {
                return intersection
            }
            if (Math.abs(d.y - c.y) + Math.abs(d.x - c.x) == 0f) {
                return intersection
            }
            if ((b.y - a.y) * (c.x - d.x) - (b.x - a.x) * (c.y - d.y) == 0f) {
                return intersection
            }
            intersection.x = ((b.x - a.x) * (c.x - d.x) * (c.y - a.y) -
                    c.x * (b.x - a.x) * (c.y - d.y) + a.x * (b.y - a.y) * (c.x - d.x)) /
                    ((b.y - a.y) * (c.x - d.x) - (b.x - a.x) * (c.y - d.y))
            intersection.y = (((b.y - a.y) * (c.y - d.y) * (c.x - a.x) - (c.y
                    * (b.y - a.y) * (c.x - d.x)) + a.y * (b.x - a.x) * (c.y - d.y))
                    / ((b.x - a.x) * (c.y - d.y) - (b.y - a.y) * (c.x - d.x)))
            return intersection
        }

        fun getIntersectionAngle(
            a: TouchPoint,
            b: TouchPoint,
            c: TouchPoint,
            d: TouchPoint
        ):Float{
            val dx1 = b.x - a.x
            val dy1 = b.y - a.y
            val dx2 = d.x - c.x
            val dy2 = d.y - c.y
            val dotProduct = dx1 * dx2 + dy1 * dy2
            val crossProduct = dx1 * dy2 - dy1 * dx2
            val magnitudeAB = sqrt(dx1 * dx1 + dy1 * dy1)
            val magnitudeCD = sqrt(dx2 * dx2 + dy2 * dy2)
            var cosTheta = dotProduct / (magnitudeAB * magnitudeCD)
            cosTheta = max(-1.0f, min(1.0f, cosTheta))
            val angle = acos(cosTheta)
            var angleInDegrees = Math.toDegrees(angle.toDouble())
            if (crossProduct < 0) {
                angleInDegrees = -angleInDegrees
            }
            return angleInDegrees.toFloat()
        }

        fun renderPointArray(points: List<TouchPoint>): FloatArray? {
            return renderPointArray(points, FLOAT_ONE)
        }

        fun renderPointArray(points: List<TouchPoint>, pointScale: Float): FloatArray {
            val array = FloatArray(points.size * 3)
            for (i in points.indices) {
                val index = 3 * i
                val p = points[i]
                array[index] = p.x * pointScale
                array[index + 1] = p.y * pointScale
                array[index + 2] = p.size
            }
            return array
        }

        fun realPointArray(points: List<TouchPoint>): FloatArray? {
            return realPointArray(points, FLOAT_ONE)
        }

        fun realPointArray(points: List<TouchPoint>, pointScale: Float): FloatArray? {
            if (points.isNullOrEmpty()) {
                return floatArrayOf()
            }
            val firstPointTime = points[0].timestamp
            val pointArray = FloatArray(points.size * 5)
            for (i in points.indices) {
                val index = 5 * i
                val p = points[i]
                pointArray[index] = p.x * pointScale
                pointArray[index + 1] = p.y * pointScale
                pointArray[index + 2] = p.size
                pointArray[index + 3] = p.pressure
                pointArray[index + 4] = (p.timestamp - firstPointTime).toFloat()
            }
            return pointArray
        }

        fun scalePointList(points: List<TouchPoint>, pointScale: Float): List<TouchPoint> {
            return points
                .map {
                    TouchPoint(it.x * pointScale, it.y * pointScale, it.pressure, it.size, it.timestamp)
                }.toList()
        }

        fun fromTinyPoint(tinyPoint: TinyPoint, maxPressure: Float = FLOAT_ONE): TouchPoint {
            // if there is an short overflow, restore it
            val pressure = if (tinyPoint.pressure < 0f) {
                (tinyPoint.pressure.toInt() and 0xFFFF).toFloat()
            } else {
                tinyPoint.pressure.toFloat()
            } / maxPressure
            val touchPoint = TouchPoint(
                tinyPoint.x,
                tinyPoint.y,
                min(pressure, 1f),
                tinyPoint.size.toFloat(),
                tinyPoint.time.toLong()
            )
            size2Tilt(tinyPoint, touchPoint)
            return touchPoint
        }

        // TinyPoint.pressure: Short = 0, convert touchPoint [0-1] press to TinyPoint.short will be 0
        fun toTinyPoint(
            point: TouchPoint,
            compareTime: Long,
            maxPressure: Float = FLOAT_ONE
        ): TinyPoint {
            val tinyPoint = TinyPoint(
                point.x,
                point.y,
                (point.pressure * maxPressure).toInt().toShort(),
                point.size.toInt().toShort(),
                (point.timestamp - compareTime).toInt()
            )
            tilt2Size(point, tinyPoint)
            return tinyPoint
        }

        fun size2Tilt(srcPoint: TinyPoint, dstPoint: TouchPoint) {
            dstPoint.tiltX = srcPoint.size.toByte().toInt()
            dstPoint.tiltY = (srcPoint.size.toInt() shr 8).toByte().toInt()
        }

        fun tilt2Size(srcPoint: TouchPoint, dstPoint: TinyPoint) {
            val size = (srcPoint.tiltY shl 8) or (srcPoint.tiltX and 0xff)
            dstPoint.size = size.toShort()
        }

        fun getTouchPointCoordinatesHashCode(
            touchPoint: TouchPoint,
            maxXY: Int,
            blockSize: Int
        ): Int {
            var result = (touchPoint.x / blockSize).toInt()
            result = (maxXY * result + touchPoint.y / blockSize).toInt()
            return result
        }

        fun computePointByteSize(touchPointList: List<TouchPoint?>): Int {
            return if (touchPointList.isNullOrEmpty()) {
                0
            } else {
                OBJECT_BYTE_COUNT * touchPointList.size
            }
        }

        fun getRectPoints(rectF: RectF): List<TouchPoint> {
            val list: MutableList<TouchPoint> = ArrayList();
            list.add(TouchPoint(rectF.left, rectF.top))
            list.add(TouchPoint(rectF.right, rectF.top))
            list.add(TouchPoint(rectF.right, rectF.bottom))
            list.add(TouchPoint(rectF.left, rectF.bottom))
            return list
        }

        private fun floatEquals(lhs: Float, rhs: Float, tolerance: Float): Boolean {
            return abs(lhs - rhs) < tolerance
        }

        fun computeAndroidTiltXY(tilt: Float, orientation: Float): Pair<Int, Int> {
            val tolerance = 1e-3f

            if (floatEquals(orientation, 0f, tolerance)) {
                return Pair(0, round(Math.toDegrees(acos(cos(tilt)).toDouble())).toInt())
            } else if (floatEquals(orientation, PI.toFloat() / 2, tolerance)) {
                return Pair(round(-Math.toDegrees(acos(cos(tilt)).toDouble())).toInt(), 0)
            } else if (floatEquals(orientation, -PI.toFloat() / 2, tolerance)) {
                return Pair(round(Math.toDegrees(acos(cos(tilt)).toDouble())).toInt(), 0)
            } else if (floatEquals(orientation, -PI.toFloat(), tolerance)) {
                return Pair(0, round(-Math.toDegrees(acos(cos(tilt)).toDouble())).toInt())
            } else {
                val cosT = cos(tilt)
                val tanO = tan(orientation)
                val tt = cosT * cosT
                val oo = tanO * tanO
                val a = oo
                val b = 1 - oo
                val bb = b * b
                val c = -tt
                val x = (-b + sqrt(bb - 4 * a * c)) / (2 * a)
                val sqrtX = sqrt(x)
                val tyRadian = acos(sqrtX)
                val txRadian = acos(cosT / cos(tyRadian))
                var tiltYDegrees = round(Math.toDegrees(tyRadian.toDouble())).toInt()
                var tiltXDegrees = round(Math.toDegrees(txRadian.toDouble())).toInt()

                if (-PI <= orientation && orientation <= -PI / 2.0) {
                    tiltYDegrees = -tiltYDegrees
                } else if (-PI / 2.0 < orientation && orientation <= 0) {
                    // do nothing
                } else if (0 < orientation && orientation <= PI / 2.0) {
                    tiltXDegrees = -tiltXDegrees
                } else if (PI / 2.0 < orientation && orientation <= PI) {
                    tiltXDegrees = -tiltXDegrees
                    tiltYDegrees = -tiltYDegrees
                }

                return Pair(tiltXDegrees, tiltYDegrees)
            }
        }

        fun parseTouchPointsFromPath(path: Path, step: Int = 2): List<TouchPoint> {
            val pointsList = mutableListOf<TouchPoint>()
            val pos = FloatArray(2)
            val pathMeasure = PathMeasure(path, false)
            while (true) {
                val length = pathMeasure.length
                var i = 0f
                while (length > 0 && i <= length) {
                    if (pathMeasure.getPosTan(i, pos, null)) {
                        pointsList.add(TouchPoint(pos[0], pos[1], 1.0f))
                    }
                    i += step
                    if (i > length && i != length + step) {
                        i = length
                    }
                }
                // append last point
                val ret = pathMeasure.getPosTan(length, pos, null)
                if (ret) {
                    val originLast = TouchPoint(pos[0], pos[1])
                    val currentLast = pointsList.lastOrNull()
                    if (currentLast != null && !currentLast.equalXY(originLast)) {
                        pointsList.add(originLast)
                    }
                }
                if (!pathMeasure.nextContour()) {
                    break
                }
            }
            return pointsList
        }

        fun TouchPoint.distance(touchPoint: TouchPoint): Double {
            return MathUtils.distance(x, y, touchPoint.x, touchPoint.y)
        }
    }
}