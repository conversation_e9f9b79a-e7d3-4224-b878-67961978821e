package com.onyx.android.sdk.base.data

import android.graphics.RectF

/**
 * <pre>
 *     author : suicheng
 *     time   : 2023/9/22 17:53
 *     desc   :
 * </pre>
 */
data class SizeF(var width: Float = 0f, var height: Float = 0f) {

    fun ratioPageSize(maxPageSize: Size): RectF {
        val maxSizeValue = maxPageSize.width.coerceAtLeast(maxPageSize.height)
        val bitmapMaxSize = width.coerceAtLeast(height)
        if (maxSizeValue > bitmapMaxSize || maxPageSize.isEmpty()) {
            return RectF(0f, 0f, width, height)
        }
        var maxWidth = 0
        var maxHeight = 0
        if (isVertical()) {
            maxWidth = maxPageSize.width
            maxHeight = maxPageSize.height
        } else {
            maxWidth = maxPageSize.height
            maxHeight = maxPageSize.width
        }
        val ratio = (width / maxWidth).coerceAtLeast(height / maxHeight)
        return RectF(0f, 0f, width / ratio, height / ratio)
    }

    fun isVertical(): Boolean {
        return height > width
    }

}
