package com.onyx.android.sdk.base.lite.utils

import android.graphics.Matrix
import kotlin.math.abs
import kotlin.math.hypot
import kotlin.math.max
import kotlin.math.min

object MathUtils {

    const val FLOAT_ONE = 1.0f
    private val EMPTY_MATRIX = Matrix()

    fun isEmptyMatrix(matrix: Matrix?): Boolean {
        return matrix == null || matrix == EMPTY_MATRIX
    }

    fun parseFloat(str: String?, defaultValue: Float): Float {
        return if (str == null) {
            defaultValue
        } else try {
            str.toFloat()
        } catch (nfe: NumberFormatException) {
            defaultValue
        }
    }

    fun parseFloat(str: String): Float {
        return parseFloat(str, 0.0f)
    }

    fun distance(x1: Float, y1: Float, x2: Float, y2: Float): Double {
        return hypot(abs(x1 - x2).toDouble(), abs(y1 - y2).toDouble())
    }

    fun Float.limitIn(start: Float, end: Float, startPadding: Float = 0f, endPadding: Float = 0f): Float {
        val range = min(start, end) + startPadding..max(start, end) - endPadding
        val limited = coerceIn(range)
        return limited
    }

    fun normalizeAngleTo36(angle: Double): UByte {
        val angleInt = (angle * 0.1).toInt().coerceIn(0, 36)
        if (angleInt == 36) {
            return 0.toUByte()
        }
        return angleInt.toUByte()
    }
}