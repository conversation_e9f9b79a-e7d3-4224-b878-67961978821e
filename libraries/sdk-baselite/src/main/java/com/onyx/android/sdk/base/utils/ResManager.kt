package com.onyx.android.sdk.base.utils

import android.annotation.TargetApi
import android.content.Context
import android.content.pm.ApplicationInfo
import android.content.pm.PackageInfo
import android.content.res.ColorStateList
import android.content.res.Resources
import android.content.res.TypedArray
import android.graphics.Color
import android.graphics.Point
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.os.Build
import android.util.DisplayMetrics
import android.util.TypedValue
import android.view.Surface
import android.view.ViewConfiguration
import android.view.WindowManager
import androidx.annotation.ArrayRes
import androidx.annotation.ColorInt
import com.onyx.android.sdk.base.data.Size
import com.onyx.android.sdk.base.lite.extension.IntList
import com.onyx.android.sdk.base.lite.extension.toIntList
import com.onyx.android.sdk.base.lite.utils.MathUtils
import org.w3c.dom.Document
import java.io.InputStream
import java.lang.ref.WeakReference
import javax.xml.parsers.DocumentBuilderFactory
import kotlin.math.sqrt

/**
 * <pre>
 *     author : suicheng
 *     time   : 2023/9/19 10:02
 *     desc   :
 * </pre>
 */
object ResManager {

    const val INVALID_RESOURCE_ID = 0
    const val INVALID_DIMENS = -1

    private lateinit var appContext: Context
    private var uiContextReference = WeakReference<Context?>(null)

    fun init(context: Context) {
        appContext = context
    }

    fun installUiContext(uiContext: Context?) {
        uiContextReference = WeakReference(uiContext)
    }

    fun uninstallUiContext() {
        uiContextReference.clear()
    }

    fun getContext(): Context {
        val context = uiContextReference.get()
        return context ?: appContext
    }

    fun getUiContext() = uiContextReference.get()

    fun getString(resId: Int): String? {
        return getString(getContext(), resId)
    }

    fun getString(context: Context?, resId: Int): String? {
        return context!!.resources.getString(resId)
    }

    fun getStringSafely(resId: Int): String {
        return getStringSafely(getContext(), resId)
    }

    fun getStringSafely(context: Context?, resId: Int): String {
        return try {
            getString(context, resId) ?: ""
        } catch (e: Exception) {
            ""
        }
    }

    fun getFormatString(resId: Int, vararg formatArgs: Any): String {
        return try {
            getContext().resources.getString(resId, *formatArgs)
        } catch (e: Exception) {
            ""
        }
    }

    fun getString(resName: String?): String? {
        return getString(getIdentifier(resName, "string"))
    }

    fun getStringSafely(resName: String?): String? {
        return getStringSafely(getContext(), resName)
    }

    fun getStringSafely(resName: String?, default: String = ""): String? {
        val id = getIdentifier(resName, "string")
        return if (id <= 0) {
            default
        } else {
            getStringSafely(id)
        }
    }

    fun getStringSafely(context: Context?, resName: String?): String? {
        val id = getIdentifier(resName, "string")
        return if (id <= 0) {
            ""
        } else getStringSafely(context, id)
    }

    fun getRawIdentifier(name: String?): Int {
        return getIdentifier(name, "raw")
    }

    fun getString(resId: Int, vararg formatArgs: Any?): String {
        return getContext()!!.resources.getString(resId, *formatArgs)
    }

    fun getStringSafely(resId: Int, vararg formatArgs: Any?): String {
        return try {
            getString(resId, *formatArgs)
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }
    }

    fun getInteger(resId: Int): Int {
        return getContext()!!.resources.getInteger(resId)
    }

    fun getInteger(context: Context?, resId: Int): Int {
        return context?.resources?.getInteger(resId) ?: getInteger(resId)
    }

    fun getLong(resId: Int) = getInteger(resId).toLong()

    fun getFloat(resId: Int): Float {
        val outValue = TypedValue()
        getContext()!!.resources.getValue(resId, outValue, true)
        return outValue.float
    }

    fun getFraction(resId: Int) = appContext.resources.getFraction(resId, 1, 0)

    fun getStringArray(resId: Int): Array<String> {
        return getContext()!!.resources.getStringArray(resId)
    }

    fun getStringArray(resName: String?): Array<String?>? {
        val id = getIdentifier(resName, "array")
        return if (id <= 0) {
            arrayOf()
        } else getContext()!!.resources.getStringArray(id)
    }

    fun getTypedArray(resId: Int): TypedArray? {
        return getContext()!!.resources.obtainTypedArray(resId)
    }

    fun getDimens(resId: Int): Int {
        return getContext()!!.resources.getDimensionPixelSize(resId)
    }

    fun getDimensSafely(resId: Int): Int {
        return try {
            getDimens(resId)
        } catch (e: Resources.NotFoundException) {
            INVALID_DIMENS
        }
    }

    fun getColor(res: Int): Int {
        return getContext()!!.resources.getColor(res)
    }

    fun getColorList(@ArrayRes resId: Int): IntList {
        return getContext()!!.resources.getIntArray(resId).toIntList()
    }

    fun getColorStateList(res: Int): ColorStateList? {
        return getContext()!!.resources.getColorStateList(res)
    }

    fun getIntArray(resId: Int): IntArray {
        return getContext()!!.resources.getIntArray(resId)
    }

    fun getIntegerArray(resId: Int): Array<Int?>? {
        val array = getIntArray(resId)
        val objectArray = arrayOfNulls<Int>(array.size)
        for (ctr in array.indices) {
            objectArray[ctr] = Integer.valueOf(array[ctr]) // returns Integer value
        }
        return objectArray
    }

    fun getFloatArray(resId: Int): Array<Float?>? {
        val array = getContext()!!.resources.getStringArray(resId)
        val objectArray = arrayOfNulls<Float>(array.size)
        for (ctr in array.indices) {
            objectArray[ctr] = MathUtils.parseFloat(array[ctr])
        }
        return objectArray
    }

    fun getUriOfRawName(rawName: String): String? {
        return "file:///android_res/raw/$rawName"
    }

    fun getUriOfAssets(htmlName: String): String? {
        return "file:///android_asset/$htmlName"
    }

    fun getDimension(resId: Int): Float {
        return getContext()!!.resources.getDimension(resId)
    }

    fun getDimensionPixelSize(resId: Int): Int {
        return getContext()!!.resources.getDimensionPixelSize(resId)
    }

    fun getFloatValue(floatDimen: Int): Float {
        val typedValue = TypedValue()
        getResources().getValue(floatDimen, typedValue, true)
        return typedValue.float
    }

    fun getDrawable(name: String?): Drawable? {
        val resId = getDrawableResIdSafely(name)
        return if (resId <= 0) {
            null
        } else getDrawable(resId)
    }

    fun getDrawable(resId: Int): Drawable? {
        return getContext()!!.resources.getDrawable(resId)
    }

    fun getDrawableResId(resName: String?): Int {
        return getIdentifier(resName, "drawable")
    }

    fun getDrawableResIdSafely(resName: String?): Int {
        return try {
            getDrawableResId(resName)
        } catch (e: Exception) {
            0
        }
    }

    fun getScreenWidth(): Float {
        return appContext!!.resources.displayMetrics.widthPixels.toFloat()
    }

    fun getScreenHeight(): Float {
        return appContext!!.resources.displayMetrics.heightPixels.toFloat()
    }

    fun getLongerScreenSize(): Float {
        return Math.max(getScreenWidth(), getScreenHeight())
    }

    fun getShorterScreenSize(): Float {
        return Math.min(getScreenWidth(), getScreenHeight())
    }

    fun getLongerRealScreenSize(): Int {
        val screenRect = getRealScreenRect()
        return Math.max(screenRect.width(), screenRect.height())
    }

    fun getShorterRealScreenSize(): Int {
        val screenRect = getRealScreenRect()
        return screenRect.width().coerceAtMost(screenRect.height())
    }

    fun getScreenRect(): Rect {
        return Rect(0, 0, getShorterScreenSize().toInt(), getLongerScreenSize().toInt())
    }

    fun getScreenRectF(): RectF {
        return RectF(0f, 0f, getShorterScreenSize(), getLongerScreenSize())
    }

    fun getScreenSize() = Size(getShorterScreenSize().toInt(), getLongerScreenSize().toInt())

    fun getRealScreenArea(): Int {
        val realScreenRect = getRealScreenRect()
        return realScreenRect.width() * realScreenRect.height()
    }

    fun getScreenArea(): Int {
        return (getScreenWidth() * getScreenHeight()).toInt()
    }

    /**
     * this will get the real screen height that not affected by navigation bar
     * @return real screen height
     */
    @TargetApi(17)
    fun getRealScreenHeight(context: Context): Int {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val outSize = Point(0, 0)
        windowManager?.defaultDisplay?.getRealSize(outSize)
        return outSize.y
    }

    // physical size
    fun getScreenRealSize() = getRealScreenRect().run { Size(width(), height()) }

    // content size
    // without navigation bar
    fun getScreenContentSize() = Size().apply {
        width = getScreenWidth().toInt()
        height = getScreenHeight().toInt()
    }

    /**
     * this will get the real screen rect that not affected by navigation bar
     * @return real screen rect
     */
    @TargetApi(17)
    fun getRealScreenRect(): Rect {
        val windowManager = appContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val outSize = Point(0, 0)
        windowManager?.defaultDisplay?.getRealSize(outSize)
        return Rect(0, 0, outSize.x, outSize.y)
    }

    fun getWindowDefaultWidth(): Int {
        return getWindowDefaultWidth(getContext())
    }

    fun getWindowDefaultWidth(context: Context?): Int {
        return getWindowDefaultSize(context).x
    }

    fun getWindowDefaultHeight(): Int {
        return getWindowDefaultHeight(getContext())
    }

    fun getWindowDefaultHeight(context: Context?): Int {
        return getWindowDefaultSize(context).y
    }

    fun isLandscapeWindows(): Boolean {
        return getWindowDefaultWidth() > getWindowDefaultHeight()
    }

    fun getWindowDefaultSize(context: Context?): Point {
        val windowManager = context!!.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val outSize = Point(0, 0)
        if (windowManager != null) {
            windowManager.defaultDisplay.getRealSize(outSize)
            updateWindowDefaultSizeInMultiWindowModeCompatM(context, outSize)
        }
        return outSize
    }

    fun getWindowDefaultRect(): Rect {
        val point = getWindowDefaultSize(getContext())
        return Rect(0, 0, point.x, point.y)
    }

    private fun updateWindowDefaultSizeInMultiWindowModeCompatM(
        context: Context?,
        realSize: Point
    ) {
        if (!false) {
            return
        }
        if (realSize.x > realSize.y) {
            realSize[realSize.x / 2] = realSize.y
        } else {
            realSize[realSize.x] = realSize.y / 2
        }
    }

    fun getAspectRatio(): Float {
        return getScreenWidth() / getScreenHeight()
    }

    fun getAppContext(): Context {
        return appContext
    }

    fun getDPI(): Int {
        return getContext()!!.resources.displayMetrics.densityDpi
    }

    fun getIdentifier(name: String?, type: String?): Int {
        return getContext()!!.resources.getIdentifier(name, type, getContext()!!.packageName)
    }

    fun openRawResource(name: String?): InputStream? {
        return getContext()!!.resources.openRawResource(getIdentifier(name, "raw"))
    }

    fun openRawResource(id: Int): InputStream? {
        return getContext()!!.resources.openRawResource(id)
    }

    fun getQuantityString(resId: Int, quantity: Int): String? {
        return getContext()!!.resources.getQuantityString(resId, quantity)
    }

    fun getQuantityString(resId: Int, quantity: Int, vararg formatArgs: Any?): String? {
        return getContext()!!.resources.getQuantityString(resId, quantity, *formatArgs)
    }

    fun getBoolean(id: Int): Boolean {
        return getContext()!!.resources.getBoolean(id)
    }

    fun getResources(): Resources {
        return getContext()!!.resources
    }

    fun getResourceEntryName(id: Int): String? {
        return try {
            if (id <= 0) {
                null
            } else getResources().getResourceEntryName(id)
        } catch (ignored: Exception) {
            null
        }
    }

    fun getDensityDpi(): Int {
        return getAppContext()!!.resources.displayMetrics.densityDpi
    }

    fun getDensity(): Float {
        return getAppContext()!!.resources.displayMetrics.density
    }

    fun isDeviceDpi(targetDPI: Int): Boolean {
        return getDensityDpi() == targetDPI
    }

    fun getDrawableForDensity(packageName: String?, resourceId: Int, density: Int): Drawable? {
        return if (resourceId <= 0) {
            null
        } else try {
            val packageInfo: PackageInfo =
                getContext()!!.packageManager.getPackageInfo(packageName!!, 0);
            val resources =
                getContext()!!.packageManager.getResourcesForApplication(packageInfo.applicationInfo)
            resources.getDrawableForDensity(resourceId, density)
        } catch (ignored: Exception) {
            null
        }
    }

    /**
     * Try to guess if the color on top of the given `colorOnBottomInt`
     * should be light or dark. Returns true if top color should be light
     */
    fun shouldColorOnTopBeLight(@ColorInt colorOnBottomInt: Int): Boolean {
        return 186 > (0.299 * Color.red(colorOnBottomInt)
                + (0.587 * Color.green(colorOnBottomInt)
                + 0.114 * Color.blue(colorOnBottomInt)))
    }

    fun loadXmlWithDom(xmlResId: Int): Document? {
        try {
            getResources().openRawResource(xmlResId).use { inputStream ->
                val documentBuilderFactory =
                    DocumentBuilderFactory.newInstance()
                val documentBuilder =
                    documentBuilderFactory.newDocumentBuilder()
                return documentBuilder.parse(inputStream)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }

    fun getMinDragThreshold(): Int {
        return getScaledTouchSlop() / 4
    }

    fun getScaledTouchSlop(): Int {
        return ViewConfiguration.get(appContext).scaledTouchSlop
    }

    fun getRotation(): Int {
        val windowManager = getAppContext().getSystemService(Context.WINDOW_SERVICE) as WindowManager?
            ?: return Surface.ROTATION_0
        return windowManager.defaultDisplay.rotation
    }

    fun isLandscapeRotation(rotation: Int) = when (rotation) {
        Surface.ROTATION_90, Surface.ROTATION_270 -> true
        else -> false
    }

    fun isPortraitRotation(rotation: Int) = when (rotation) {
        Surface.ROTATION_0, Surface.ROTATION_180 -> true
        else -> false
    }

    fun getScreenInchSize(): Double {
        val windowManager = getContext()?.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val metrics = DisplayMetrics()
        val display = windowManager.defaultDisplay
        val point = Point()

        display.getMetrics(metrics)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            display?.getRealSize(point)
        } else {
            display.getSize(point)
        }
        val w: Double = (point.x / metrics.xdpi).toDouble() // unit is inch
        val h: Double = (point.y / metrics.ydpi).toDouble() // unit is inch
        val size = sqrt(w * w + h * h)

        return size
    }

    fun isAppDebuggable(): Boolean {
        return (getAppContext().applicationInfo.flags and ApplicationInfo.FLAG_DEBUGGABLE) != 0
    }
}