package com.onyx.android.sdk.base.lite.extension

import android.graphics.Path
import android.graphics.RectF

/**
 * <pre>
 *     author : suicheng
 *     time   : 2023/10/18 18:52
 *     desc   :
 * </pre>
 */

fun Path.copy(): Path {
    val path = Path()
    path.addPath(this)
    return path
}

fun getQuadToEndPos(current: Float, next: Float, factor: Float = 0.7f): Float {
    // TODO: may use 0.5~0.7 value
    return current + (next - current) * factor
}