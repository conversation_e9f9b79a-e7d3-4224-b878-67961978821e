package com.onyx.android.sdk.base.data

import android.graphics.Matrix
import android.graphics.PointF
import android.graphics.RectF
import com.onyx.android.sdk.base.lite.utils.MathUtils.FLOAT_ONE
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.DataInputStream
import java.io.DataOutputStream
import java.io.Serializable

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 10/3/23
 * Not necessary to support json now.
 *
 */
class TouchPointList : Serializable, Cloneable {

    private var points: MutableList<TouchPoint>


    constructor() {
        points = ArrayList()
    }

    constructor(size: Int) {
        points = ArrayList(size)
    }

    constructor(other: TouchPointList) {
        points = ArrayList()
        addAll(other)
    }

    constructor(list: List<TouchPoint>?) {
        points = ArrayList()
        list?.forEach {
            points.add(it)
        }
    }

    fun getPoints(): List<TouchPoint> {
        return points
    }

    fun setPoints(list: MutableList<TouchPoint>?): TouchPointList {
        list?.apply {
            points = this
        }
        return this
    }

    fun first(): TouchPoint? {
        return points.firstOrNull()
    }

    fun last(): TouchPoint? {
        return points.lastOrNull()
    }

    fun getRenderPoints(): List<TouchPoint> {
        return points
        /*
        TODO: re-sample, should pass args from outside
        val size = size()
        if (size <= SHAPE_LIMIT_RENDER_TOUCH_POINT_COUNT) {
            return points
        }
        val p: Int = PointDocumentUtils.getPointCountBreakValue(size)
        val touchPoints: MutableList<TouchPoint> = ArrayList()
        var i = 0
        while (i < size) {
            touchPoints.add(points[i])
            i += p
        }
        return touchPoints

         */
    }



    fun size(): Int {
        return points.size
    }

    operator fun get(i: Int): TouchPoint {
        return points[i]
    }

    fun add(touchPoint: TouchPoint) {
        points.add(touchPoint)
    }

    fun add(index: Int, touchPoint: TouchPoint) {
        points.add(index, touchPoint)
    }

    fun addAll(other: TouchPointList) {
        points.addAll(other.getPoints())
    }

    fun addAll(index: Int, pointList: List<TouchPoint>) {
        points.addAll(index, pointList)
    }

    fun addAll(pointList: List<TinyPoint>, maxPressure: Float = FLOAT_ONE): TouchPointList {
        if (pointList.isEmpty()) {
            return this
        }
        for (tinyPoint in pointList) {
            tinyPoint.apply {
                points.add(TouchPoint.fromTinyPoint(this, maxPressure))
            }
        }
        return this
    }

    fun removeAll(list: List<TouchPoint>) {
        points.removeAll(list)
    }

    fun detachPointList(): List<TinyPoint> {
        val tinyPointList = toTinyPointList()
        points.clear()
        return tinyPointList
    }

    fun toTinyPointList(maxPressure: Float = FLOAT_ONE): List<TinyPoint> {
        val tinyPointList: MutableList<TinyPoint> = ArrayList()
        if (points.isEmpty()) {
            return tinyPointList
        }
        val compareTime = points[0].timestamp
        for (point in points) {
            tinyPointList.add(TouchPoint.toTinyPoint(point, compareTime, maxPressure))
        }
        return tinyPointList
    }

    operator fun iterator(): Iterator<TouchPoint> {
        return points.iterator()
    }

    fun applyMatrix(matrix: Matrix?): TouchPointList {
        matrix ?: return this
        if (isEmptyMatrix(matrix)) {
            return this
        }
        for (point in points) {
            val pts = floatArrayOf(point.x, point.y)
            matrix.mapPoints(pts)
            point.x = pts[0]
            point.y = pts[1]
        }
        return this
    }

    fun scaleAllPoints(scaleValue: Float) {
        for (point in points) {
            point.x = point.x * scaleValue
            point.y = point.y * scaleValue
        }
    }

    fun scaleAllPoints(sx: Float, sy: Float) {
        for (point in points) {
            point.x = point.x * Math.abs(sx)
            point.y = point.y * Math.abs(sy)
        }
    }

    fun translateAllPoints(dx: Float, dy: Float) {
        for (point in points) {
            point.x = point.x + dx
            point.y = point.y + dy
        }
    }

    fun rotateAllPoints(rotateAngle: Float, originPoint: PointF) {
        val rotateMatrix = Matrix()
        rotateMatrix.setRotate(rotateAngle, originPoint.x, originPoint.y)
        for (point in points) {
            val pts = FloatArray(2)
            pts[0] = point.x
            pts[1] = point.y
            rotateMatrix.mapPoints(pts)
            point.x = pts[0]
            point.y = pts[1]
        }
    }

    public override fun clone(): TouchPointList {
        val list = TouchPointList()
        if (points.isEmpty()) {
            return list
        }
        for (point in points) {
            if (point == null) {
                continue
            }
            list.add(point.clone())
        }
        return list
    }

    fun isEmpty(): Boolean {
        return points == null || points.isEmpty()
    }

    fun clear() {
        points = ArrayList()
    }

    fun getBoundingRect(): RectF {
        return getBoundingRect(getPoints())
    }

    fun getAveragePressure(): Float {
        return if (points.isEmpty()) {
            return 0f
        } else {
            points.sumOf { it.pressure.toDouble() }.div(points.size).toFloat()
        }
    }

    companion object {

        private val EMPTY = Matrix()
        fun isEmptyMatrix(matrix: Matrix?): Boolean {
            return matrix == null || matrix == EMPTY
        }

        /**
         * @param touchPointList
         * @return return null if failed
         */
        fun pointsToByteArray(touchPointList: TouchPointList?): ByteArray? {
            if (touchPointList == null || touchPointList.isEmpty()) {
                return ByteArray(0)
            }
            var dout: DataOutputStream? = null

            val bout = ByteArrayOutputStream()
            dout = DataOutputStream(bout)
            dout.use {
                for (p in touchPointList.getPoints()) {
                    dout.writeFloat(p.x)
                    dout.writeFloat(p.y)
                    dout.writeFloat(p.pressure)
                    dout.writeFloat(p.size)
                    dout.writeInt(p.tiltX)
                    dout.writeInt(p.tiltY)
                    dout.writeLong(p.timestamp)
                }
                return bout.toByteArray()
            }
        }


        /**
         *
         * @param blob
         * @return return null if failed
         */
        fun pointsFromByteArray(blob: ByteArray?): TouchPointList {
            val points = TouchPointList(100)
            if (blob == null) {
                return points
            }
            var bin: ByteArrayInputStream? = null
            bin = ByteArrayInputStream(blob)
            val din = DataInputStream(bin)
            din.use {
                for (i in 0 until blob.size / 32) {
                    val x = din.readFloat()
                    val y = din.readFloat()
                    val pressure = din.readFloat()
                    val size = din.readFloat()
                    val tiltX = din.readInt()
                    val tiltY = din.readInt()
                    val eventTime = din.readLong()
                    val point = TouchPoint(x, y, pressure, size, eventTime)
                    point.tiltX = tiltX
                    point.tiltY = tiltY
                    points.add(point)
                }
                return points
            }
        }

        fun getBoundingRect(list: List<TouchPoint>): RectF {
            val boundingRect = RectF()
            if (list.isEmpty()) {
                return boundingRect
            }
            val first = list.first()
            boundingRect.set(first.x, first.y, first.x, first.y)
            for (touchPoint in list) {
                boundingRect.union(touchPoint.x, touchPoint.y)
            }
            return boundingRect
        }
    }
}
