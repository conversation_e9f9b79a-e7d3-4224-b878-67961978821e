package com.onyx.android.sdk.base.utils

import android.util.Log

/**
 * <pre>
 *     author : suicheng
 *     time   : 2023/9/22 15:05
 *     desc   :
 * </pre>
 */
class Benchmark {

    private var benchmarkStart: Long = 0
    private var benchmarkEnd: Long = 0

    companion object {
        var showShortDurationInfoLog = true
        var shortDurationThreshold = 5L

        val sInstance = Benchmark()
    }

    init {
        restart()
    }

    fun restart() {
        benchmarkStart = System.currentTimeMillis()
    }

    fun report(msg: String) {
        val duration = duration()
        if (duration < shortDurationThreshold && !showShortDurationInfoLog) {
            return
        }
        Debug.i(javaClass, msg + " ---> " + duration.toString() + "ms")
    }

    fun report(tag: String, msg: String) {
        Log.i(tag, msg + " ---> " + duration() + "ms")
    }

    fun report(msg: String, reportDuration: Long) {
        val duration = duration()
        if (duration < reportDuration) {
            return
        }
        report(msg)
    }

    fun reportAndRestart(msg: String, reportDuration: Long) {
        val duration = duration()
        if (duration < reportDuration) {
            restart()
            return
        }
        reportAndRestart(msg)
    }

    fun reportAndRestart(msg: String) {
        report(msg)
        restart()
    }

    fun reportDebugAndRestart(msg: String) {
        Debug.d(javaClass, msg + " ---> " + duration() + "ms")
        restart()
    }

    fun reportError(msg: String) {
        Debug.e(javaClass, msg + " ---> " + duration().toString() + "ms")
    }

    fun reportWarn(msg: String) {
        Debug.w(javaClass, msg + " ---> " + duration().toString() + "ms")
    }

    fun reportDebug(msg: String) {
        Debug.d(javaClass, msg + " ---> " + duration() + "ms")
    }

    fun reportDebug(msg: () -> String) {
        Debug.d { msg() + " ---> " + duration() + "ms" }
    }

    fun reportDebug(tag: String, msg: String) {
        Debug.d(tag) {
            "$msg ---> ${duration()}ms"
        }
    }

    fun duration(): Long {
        benchmarkEnd = System.currentTimeMillis()
        return benchmarkEnd - benchmarkStart
    }

    fun durationString(): String {
        return "${duration()}ms"
    }
}