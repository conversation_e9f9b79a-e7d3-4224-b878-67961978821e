package com.onyx.android.sdk.base.data

import android.graphics.Rect
import android.graphics.RectF

typealias DataSize = Size

data class Size(
    var width: Int = 0,
    var height: Int = 0
) {

    fun setWidth(width: Int): Size {
        this.width = width
        return this
    }

    fun setHeight(height: Int): Size {
        this.height = height
        return this
    }

    fun isEmpty(): <PERSON><PERSON><PERSON> {
        return width <= 0 || height <= 0
    }

    fun equals(w: Int, h: Int): <PERSON><PERSON>an {
        return width == w && height == h
    }

    fun toRectF() = RectF(0f, 0f, width.toFloat(), height.toFloat())

    fun toRect() = Rect(0, 0, width, height)

    fun key() = "w=$width-h=$height"

    fun isVertical(): <PERSON><PERSON>an {
        return height > width
    }

    fun ratioPageSize(maxPageSize: Size): RectF {
        val maxSizeValue = Math.max(maxPageSize.width, maxPageSize.height)
        val bitmapMaxSize = Math.max(width, height)
        if (maxSizeValue > bitmapMaxSize) {
            return RectF(0f, 0f, width.toFloat(), height.toFloat())
        }
        var maxWidth = 0
        var maxHeight = 0
        if (isVertical()) {
            maxWidth = maxPageSize.width
            maxHeight = maxPageSize.height
        } else {
            maxWidth = maxPageSize.height
            maxHeight = maxPageSize.width
        }
        val ratio = Math.max(width.toFloat() / maxWidth, height.toFloat() / maxHeight)
        return RectF(0f, 0f, width / ratio, height / ratio)
    }

    fun getOrElse(defaultValue: () -> Size): Size {
        return if (isEmpty()) {
            defaultValue()
        } else {
            this
        }
    }

    override fun toString(): String {
        return "${width}x$height"
    }

    companion object {
        val empty = Size()

        fun android.util.Size.toDataSize(): DataSize {
            return DataSize(width, height)
        }
    }
}