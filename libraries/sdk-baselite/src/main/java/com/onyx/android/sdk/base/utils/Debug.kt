package com.onyx.android.sdk.base.utils

import android.text.TextUtils
import android.util.Log
import java.io.PrintWriter
import java.io.StringWriter
import java.util.regex.Pattern
import kotlin.reflect.KClass

/**
 * <pre>
 *     author : tom
 *     time   : 2023/9/16 11:39
 *     desc   :
 * </pre>
 */
object Debug {
    val TAG = Debug::class.java.simpleName
    var debug = false
    var isObfuscateLogEnabled = false
    var isCompareObfuscateLogEnabled = false
    private val obfuscatePattern = Pattern.compile("[a-z]+")

    fun d(msg: String?) {
        if (debug) {
            Log.d(TAG, msg!!)
        }
    }

    fun d(msg: IMessage) {
        if (debug) {
            Log.d(TAG, msg.message!!)
        }
    }

    fun d(tr: Throwable?) {
        if (debug) {
            Log.d(TAG, "", tr)
        }
    }

    fun printStackTraceDebug(msg: String) {
        printStackTraceDebug(TAG, msg)
    }

    fun printStackTraceDebug(tag: String?, msg: String) {
        if (debug) {
            val header = """Printing detailed debug information.
 Message :"""
            Log.v(tag, "", Exception(header + msg))
        }
    }

    fun v(cls: Class<*>, msg: String?, vararg args: Any?) {
        if (debug) {
            Log.v(getVerifiedTag(cls), formatString(msg, *args))
        }
    }

    fun v(cls: KClass<*>, msg: String?, vararg args: Any?) {
        v(cls.java, msg, args)
    }

    fun v(tag: String, msg: String?, vararg args: Any?) {
        if (debug) {
            Log.v(tag, formatString(msg, *args))
        }
    }

    fun d(cls: Class<*>, msg: String?, vararg args: Any?) {
        if (debug) {
            Log.d(getVerifiedTag(cls), formatString(msg, *args))
        }
    }
    fun d(cls: KClass<*>, msg: String?, vararg args: Any?) {
        d(cls.java, msg, args)
    }

    fun d(tag: String?, msg: String?, vararg args: Any?) {
        if (debug) {
            Log.d(tag, formatString(msg, *args))
        }
    }

    inline fun d(msg: () -> String) {
        if (debug) {
            Log.d(TAG, msg())
        }
    }

    inline fun d(tag: String, msg: () -> String) {
        if (debug) {
            Log.d(tag, msg())
        }
    }

    inline fun d(cls: Class<*>, msg: () -> String) {
        if (debug) {
            d(cls, msg())
        }
    }

    fun i(msg: String) {
        Log.i(TAG, obfuscateLog(msg))
    }

    fun i(tag: String?, msg: String?, vararg args: Any?) {
        val str = obfuscateLog(formatString(msg, *args))
        Log.i(tag, str)
    }

    fun i(cls: Class<*>, msg: String?, vararg args: Any?) {
        val tag = obfuscateLog(getVerifiedTag(cls))
        i(tag, msg, *args)
    }

    fun i(cls: KClass<*>, msg: String?, vararg args: Any?) {
        i(cls.java, msg, *args)
    }

    fun obfuscateLog(msg: String): String {
        if (TextUtils.isEmpty(msg)) {
            return ""
        }
        compareObfuscateLog(msg)
        return if (isObfuscateLogEnabled) {
            obfuscateLogImpl(msg)
        } else msg
    }

    private fun obfuscateLogImpl(msg: String): String {
        return obfuscatePattern.matcher(msg).replaceAll("")
    }

    private fun compareObfuscateLog(msg: String) {
        if (isCompareObfuscateLogEnabled) {
            Log.i(TAG, "raw : = $msg")
            Log.i(TAG, "obfuscate : = " + obfuscateLogImpl(msg))
        }
    }

    fun w(msg: String?) {
        Log.w(TAG, msg!!)
    }

    fun w(cls: Class<*>, msg: String?, vararg args: Any?) {
        Log.w(getVerifiedTag(cls), formatString(msg, *args))
    }

    fun w(cls: KClass<*>, msg: String?, vararg args: Any?) {
        w(cls.java, msg, args)
    }

    fun w(throwable: Throwable?) {
        Log.w(TAG, throwable)
    }

    fun w(tag: String, msg: String, vararg args: Any) {
        Log.w(tag, formatString(msg, *args))
    }

    fun w(cls: Class<*>, throwable: Throwable?) {
        Log.w(getVerifiedTag(cls), throwable)
    }

    fun w(cls: KClass<*>, throwable: Throwable?) {
        w(cls.java, throwable)
    }

    fun e(msg: String?) {
        if (msg != null) {
            Log.e(TAG, msg)
        }
    }

    fun e(tag: String?, msg: String?, vararg args: Any?) {
        Log.e(tag, formatString(msg, *args))
    }

    fun e(cls: Class<*>, msg: String?, vararg args: Any?) {
        Log.e(getVerifiedTag(cls), formatString(msg, *args))
    }

    fun e(cls: KClass<*>, msg: String?, vararg args: Any?) {
        e(cls.java, msg, args)
    }

    fun e(throwable: Throwable) {
        val sw = StringWriter()
        throwable.printStackTrace(PrintWriter(sw))
        Log.e(TAG, sw.toString())
    }

    fun e(cls: Class<*>, throwable: Throwable) {
        val sw = StringWriter()
        throwable.printStackTrace(PrintWriter(sw))
        Log.e(getVerifiedTag(cls), sw.toString())
    }

    fun e(cls: KClass<*>, throwable: Throwable) {
        e(cls.java, throwable)
    }

    fun e(cls: Class<*>, message: String?, throwable: Throwable?) {
        Log.e(getVerifiedTag(cls), message, throwable)
    }

    fun e(cls: KClass<*>, message: String?, throwable: Throwable?) {
        e(cls.java, message, throwable)
    }

    fun formatString(str: String?, vararg args: Any?): String {
        if (str == null) {
            return ""
        }
        return if (args.isEmpty()) {
            str
        } else try {
            String.format(null, str, *args)
        } catch (tr: Throwable) {
            str
        }
    }

    fun getVerifiedTag(cls: Class<*>): String {
        return verifyTag(cls.getSimpleName())
    }

    private fun verifyTag(tag: String?): String {
        return if (tag != null && !TextUtils.isEmpty(tag.trim { it <= ' ' })) {
            tag
        } else TAG
    }

    interface IMessage {
        val message: String?
    }
}