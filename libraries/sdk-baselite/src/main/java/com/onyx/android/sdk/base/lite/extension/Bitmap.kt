package com.onyx.android.sdk.base.lite.extension

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import com.onyx.android.sdk.base.utils.Debug
import kotlin.contracts.ExperimentalContracts
import kotlin.contracts.contract

@OptIn(ExperimentalContracts::class)
fun Bitmap?.isValid(): Boolean {
    contract {
        returns(true) implies (this@isValid != null)
    }
    return this != null && !isRecycled && width > 0 && height > 0
}

fun Bitmap.createScaledBitmap(newWidth: Int, newHeight: Int): Bitmap {
    return createScaledBitmap(newWidth, newHeight, false)
}

fun Bitmap.createScaledBitmap(newWidth: Int, newHeight: Int, isWhiteBG: Boolean): Bitmap {
    val scaledBitmap = Bitmap.createBitmap(newWidth, newHeight, Bitmap.Config.ARGB_8888)
    if (!isValid()) {
        Debug.e(IllegalArgumentException("invalid bitmap"))
        return scaledBitmap
    }
    if (isWhiteBG) {
        scaledBitmap.eraseColor(Color.WHITE)
    }
    val ratioX = newWidth / width.toFloat()
    val ratioY = newHeight / height.toFloat()
    val middleX = newWidth / 2.0f
    val middleY = newHeight / 2.0f
    val scaleMatrix = Matrix()
    scaleMatrix.setScale(ratioX, ratioY, middleX, middleY)
    val canvas = Canvas(scaledBitmap)
    canvas.setMatrix(scaleMatrix)
    canvas.drawBitmap(
        this,
        middleX - width / 2,
        middleY - height / 2,
        Paint(Paint.FILTER_BITMAP_FLAG)
    )
    return scaledBitmap
}