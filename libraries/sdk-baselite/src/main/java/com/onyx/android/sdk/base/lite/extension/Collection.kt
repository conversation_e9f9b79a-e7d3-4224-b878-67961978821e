package com.onyx.android.sdk.base.lite.extension

import androidx.core.math.MathUtils

fun <T> MutableCollection<T>?.add(target: T?) {
    if (this == null) {
        return
    }
    target?.let {
        add(it)
    }
}

fun <T> MutableList<T>?.ensureMutableList(): MutableList<T> {
    return this ?: mutableListOf()
}

fun <T> MutableList<T>.firstOrPut(defaultValue: () -> T): T {
    if (isEmpty()) {
        safeAdd(defaultValue())
    }
    return first()
}

fun Collection<*>?.getSize(): Int {
    return this?.size ?: 0
}

fun <T> MutableCollection<T>?.safeAdd(target: T?) {
    add(target)
}

fun <T> List<T>.safelySubList(fromIndex: Int, toIndex: Int): MutableList<T> {
    if (isNullOrEmpty()) {
        return arrayListOf()
    }
    if (fromIndex > toIndex) {
        return mutableListOf()
    }
    val size = getSize()
    return subList(
        MathUtils.clamp(fromIndex, 0, size),
        MathUtils.clamp(toIndex, 0, size)
    ).toMutableList()
}