package com.onyx.android.sdk.base

import android.graphics.Matrix
import com.onyx.android.sdk.base.data.TouchPoint
import com.onyx.android.sdk.base.utils.Debug
import org.junit.Test
import kotlin.time.measureTime

/**
 * <pre>
 *     author : liao lin tao
 *     time   : 2025/4/11 14:53
 *     desc   :
 * </pre>
 */
class TouchPointTest {

    @Test
    fun testRenderPointArray() {
        val pointList = List(10000) {
            TouchPoint(x = it.toFloat(), it.toFloat())
        }

        repeat(10) { index ->
            Debug.w("---- testRenderPointArray start $index, pointSize = ${pointList.size}----")

            var testPointList = pointList.map { it.copy() }

            val testMatrix = Matrix()
            var timed1 = measureTime {
                renderPointArrayV1(testMatrix, testPointList)
            }
            Debug.w("testRenderPointArray, v1, timed1 = $timed1, empty matrix = $testMatrix")

            testPointList = pointList.map { it.copy() }
            var timed2 = measureTime {
                TouchPoint.renderPointArray(testMatrix, testPointList)
            }
            Debug.w("testRenderPointArray, v2, timed2 = $timed2, empty matrix = $testMatrix")

            testMatrix.postScale(2.5f, 2.5f)
            testMatrix.postTranslate(-1001.2f, -1234.5f)
            testPointList = pointList.map { it.copy() }
            timed1 = measureTime {
                renderPointArrayV1(testMatrix, testPointList)
            }
            Debug.w("testRenderPointArray, v1, timed1 = $timed1, matrix = $testMatrix")

            testPointList = pointList.map { it.copy() }
            timed2 = measureTime {
                TouchPoint.renderPointArray(testMatrix, testPointList)
            }
            Debug.w("testRenderPointArray, v2, timed2 = $timed2, matrix = $testMatrix")

            Debug.w("---- testRenderPointArray finish $index----")
        }
    }

    fun renderPointArrayV1(matrix: Matrix?, touchPoints: List<TouchPoint>): List<TouchPoint> {
        if (matrix == null || matrix.isIdentity) {
            return touchPoints
        }
        val src = FloatArray(2)
        val dst = FloatArray(2)
        val result: MutableList<TouchPoint> = ArrayList()
        for (i in touchPoints.indices) {
            val iterator = touchPoints[i]
            src[0] = iterator.x
            dst[0] = src[0]
            src[1] = iterator.y
            dst[1] = src[1]
            matrix.mapPoints(dst, src)
            val touchPoint = TouchPoint(iterator)
            touchPoint.x = dst[0]
            touchPoint.y = dst[1]
            result.add(touchPoint)
        }
        return result
    }
}