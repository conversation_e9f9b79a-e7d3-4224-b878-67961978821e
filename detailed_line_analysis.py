#!/usr/bin/env python3
"""
详细的line.json连接性分析
使用多种容差值进行精确分析
"""

import json
import math
from typing import List, Dict, <PERSON><PERSON>

def analyze_with_multiple_tolerances(json_file: str):
    """使用多种容差值分析连接性"""
    
    # 加载数据
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            lines = json.load(f)
        print(f"✓ 成功加载 {len(lines)} 条线段")
    except Exception as e:
        print(f"✗ 加载文件失败: {e}")
        return
    
    # 不同的容差值
    tolerances = [0.0, 0.01, 0.1, 0.5, 1.0, 5.0]
    
    print(f"\n=== 多容差连接性分析 ===")
    print(f"线段总数: {len(lines)}")
    print(f"连接点总数: {len(lines) - 1}")
    
    for tolerance in tolerances:
        disconnections = []
        
        for i in range(len(lines) - 1):
            current_line = lines[i]
            next_line = lines[i + 1]
            
            # 当前线段的终点
            current_end_x = current_line['stop_x']
            current_end_y = current_line['stop_y']
            
            # 下一线段的起点
            next_start_x = next_line['start_x']
            next_start_y = next_line['start_y']
            
            # 计算距离
            distance = math.sqrt((next_start_x - current_end_x) ** 2 + 
                               (next_start_y - current_end_y) ** 2)
            
            if distance > tolerance:
                disconnections.append({
                    'index': i,
                    'current_id': current_line['point_id'],
                    'next_id': next_line['point_id'],
                    'distance': distance,
                    'current_end': (current_end_x, current_end_y),
                    'next_start': (next_start_x, next_start_y)
                })
        
        connected_count = (len(lines) - 1) - len(disconnections)
        connection_rate = (connected_count / (len(lines) - 1) * 100) if len(lines) > 1 else 0
        
        print(f"\n容差 {tolerance:>6.2f}px: 连接 {connected_count:>2d}/{len(lines)-1}, "
              f"断点 {len(disconnections):>2d}, 连接率 {connection_rate:>5.1f}%")
        
        # 如果有断点，显示详情
        if disconnections and len(disconnections) <= 10:
            for j, disc in enumerate(disconnections):
                print(f"  断点 #{j+1}: 线段{disc['current_id']:>2d}->线段{disc['next_id']:>2d}, "
                      f"距离 {disc['distance']:>6.3f}px")

def analyze_coordinate_precision(json_file: str):
    """分析坐标精度和连接情况"""
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            lines = json.load(f)
    except Exception as e:
        print(f"✗ 加载文件失败: {e}")
        return
    
    print(f"\n=== 坐标精度分析 ===")
    
    exact_matches = 0
    tiny_gaps = 0
    small_gaps = 0
    
    gap_details = []
    
    for i in range(len(lines) - 1):
        current_line = lines[i]
        next_line = lines[i + 1]
        
        # 当前线段的终点
        current_end_x = current_line['stop_x']
        current_end_y = current_line['stop_y']
        
        # 下一线段的起点
        next_start_x = next_line['start_x']
        next_start_y = next_line['start_y']
        
        # 计算X和Y方向的差异
        dx = abs(next_start_x - current_end_x)
        dy = abs(next_start_y - current_end_y)
        distance = math.sqrt(dx * dx + dy * dy)
        
        gap_details.append({
            'index': i,
            'current_id': current_line['point_id'],
            'next_id': next_line['point_id'],
            'dx': dx,
            'dy': dy,
            'distance': distance,
            'current_end': (current_end_x, current_end_y),
            'next_start': (next_start_x, next_start_y)
        })
        
        if distance == 0.0:
            exact_matches += 1
        elif distance < 0.01:
            tiny_gaps += 1
        elif distance < 1.0:
            small_gaps += 1
    
    print(f"精确匹配 (距离=0): {exact_matches}")
    print(f"微小间隙 (距离<0.01): {tiny_gaps}")
    print(f"小间隙 (距离<1.0): {small_gaps}")
    print(f"较大间隙 (距离>=1.0): {len(gap_details) - exact_matches - tiny_gaps - small_gaps}")
    
    # 显示最大的几个间隙
    sorted_gaps = sorted(gap_details, key=lambda x: x['distance'], reverse=True)
    
    print(f"\n前10个最大间隙:")
    for i, gap in enumerate(sorted_gaps[:10]):
        if gap['distance'] > 0:
            print(f"  #{i+1}: 线段{gap['current_id']:>2d}->线段{gap['next_id']:>2d}, "
                  f"距离 {gap['distance']:>8.5f}px, "
                  f"dx={gap['dx']:>8.5f}, dy={gap['dy']:>8.5f}")
            print(f"       终点: ({gap['current_end'][0]:>10.5f}, {gap['current_end'][1]:>10.5f})")
            print(f"       起点: ({gap['next_start'][0]:>10.5f}, {gap['next_start'][1]:>10.5f})")

def check_line_sequence(json_file: str):
    """检查线段序列的连续性"""
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            lines = json.load(f)
    except Exception as e:
        print(f"✗ 加载文件失败: {e}")
        return
    
    print(f"\n=== 线段序列检查 ===")
    
    # 检查point_id是否连续
    point_ids = [line['point_id'] for line in lines]
    expected_ids = list(range(1, len(lines) + 1))
    
    if point_ids == expected_ids:
        print("✓ point_id序列连续 (1 到 {})".format(len(lines)))
    else:
        print("✗ point_id序列不连续")
        missing_ids = set(expected_ids) - set(point_ids)
        extra_ids = set(point_ids) - set(expected_ids)
        if missing_ids:
            print(f"  缺失的ID: {sorted(missing_ids)}")
        if extra_ids:
            print(f"  额外的ID: {sorted(extra_ids)}")
    
    # 检查时间戳
    timestamps = [line.get('timestamp', 0) for line in lines]
    if all(t > 0 for t in timestamps):
        print("✓ 所有线段都有时间戳")
        if timestamps == sorted(timestamps):
            print("✓ 时间戳按顺序排列")
        else:
            print("⚠ 时间戳顺序不一致")
    else:
        print("⚠ 部分线段缺少时间戳")
    
    # 显示基本统计
    print(f"\n基本统计:")
    print(f"  线段数量: {len(lines)}")
    print(f"  point_id范围: {min(point_ids)} - {max(point_ids)}")
    if timestamps and all(t > 0 for t in timestamps):
        print(f"  时间戳范围: {min(timestamps)} - {max(timestamps)}")
        print(f"  总时长: {max(timestamps) - min(timestamps)} ms")

def main():
    """主函数"""
    print("=" * 70)
    print("Line.json 详细连接性分析工具")
    print("=" * 70)
    
    json_file = 'line.json'
    
    # 多容差分析
    analyze_with_multiple_tolerances(json_file)
    
    # 坐标精度分析
    analyze_coordinate_precision(json_file)
    
    # 序列检查
    check_line_sequence(json_file)
    
    print(f"\n" + "=" * 70)
    print("详细分析完成！")
    print("=" * 70)
    
    return 0

if __name__ == '__main__':
    exit(main())
