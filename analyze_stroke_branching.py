#!/usr/bin/env python3
"""
分析stroke分叉问题
专门分析为什么某个stroke会出现分叉现象
"""

import json
import math
from typing import List, Dict, Tuple, Optional

class StrokeBranchingAnalyzer:
    def __init__(self):
        self.data = None
        self.target_stroke = None
    
    def load_data(self, json_file: str):
        """加载JSON数据"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"✓ 成功加载数据，包含 {len(self.data.get('strokes', []))} 个stroke")
            return True
        except Exception as e:
            print(f"✗ 加载文件失败: {e}")
            return False
    
    def find_stroke(self, stroke_id: int):
        """查找指定的stroke"""
        if not self.data:
            return None
        
        for stroke in self.data.get('strokes', []):
            if stroke.get('stroke_id') == stroke_id:
                self.target_stroke = stroke
                return stroke
        return None
    
    def analyze_stroke_connectivity(self, stroke_id: int):
        """分析stroke的连接性"""
        stroke = self.find_stroke(stroke_id)
        if not stroke:
            print(f"✗ 未找到stroke_id {stroke_id}")
            return
        
        lines = stroke.get('lines', [])
        if not lines:
            print(f"✗ Stroke {stroke_id} 没有线段数据")
            return
        
        print(f"\n=== Stroke {stroke_id} 连接性分析 ===")
        print(f"线段总数: {len(lines)}")
        print(f"时间戳范围: {stroke.get('start_timestamp', 'N/A')} - {stroke.get('end_timestamp', 'N/A')}")
        
        # 分析连接性
        disconnections = []
        exact_connections = 0
        
        for i in range(len(lines) - 1):
            current_line = lines[i]
            next_line = lines[i + 1]
            
            # 当前线段终点
            curr_end_x = current_line['stop_x']
            curr_end_y = current_line['stop_y']
            
            # 下一线段起点
            next_start_x = next_line['start_x']
            next_start_y = next_line['start_y']
            
            # 计算距离
            dx = next_start_x - curr_end_x
            dy = next_start_y - curr_end_y
            distance = math.sqrt(dx * dx + dy * dy)
            
            if distance == 0.0:
                exact_connections += 1
            else:
                disconnections.append({
                    'index': i,
                    'current_point': current_line['point_id'],
                    'next_point': next_line['point_id'],
                    'current_end': (curr_end_x, curr_end_y),
                    'next_start': (next_start_x, next_start_y),
                    'dx': dx,
                    'dy': dy,
                    'distance': distance,
                    'current_timestamp': current_line.get('timestamp'),
                    'next_timestamp': next_line.get('timestamp')
                })
        
        print(f"精确连接: {exact_connections}/{len(lines)-1}")
        print(f"断点数量: {len(disconnections)}")
        
        if disconnections:
            print(f"\n发现 {len(disconnections)} 个断点:")
            for i, disc in enumerate(disconnections):
                print(f"  断点 #{i+1}: 点{disc['current_point']} -> 点{disc['next_point']}")
                print(f"    终点: ({disc['current_end'][0]:.3f}, {disc['current_end'][1]:.3f})")
                print(f"    起点: ({disc['next_start'][0]:.3f}, {disc['next_start'][1]:.3f})")
                print(f"    偏移: dx={disc['dx']:.3f}, dy={disc['dy']:.3f}")
                print(f"    距离: {disc['distance']:.3f} 像素")
                if disc['current_timestamp'] and disc['next_timestamp']:
                    time_gap = disc['next_timestamp'] - disc['current_timestamp']
                    print(f"    时间间隔: {time_gap} ms")
                print()
        
        return disconnections
    
    def analyze_coordinate_jumps(self, stroke_id: int, threshold: float = 10.0):
        """分析坐标跳跃"""
        stroke = self.find_stroke(stroke_id)
        if not stroke:
            return
        
        lines = stroke.get('lines', [])
        print(f"\n=== Stroke {stroke_id} 坐标跳跃分析 ===")
        print(f"跳跃阈值: {threshold} 像素")
        
        jumps = []
        
        for i in range(len(lines) - 1):
            current_line = lines[i]
            next_line = lines[i + 1]
            
            # 计算线段间的跳跃
            curr_end_x = current_line['stop_x']
            curr_end_y = current_line['stop_y']
            next_start_x = next_line['start_x']
            next_start_y = next_line['start_y']
            
            jump_distance = math.sqrt((next_start_x - curr_end_x) ** 2 + 
                                    (next_start_y - curr_end_y) ** 2)
            
            if jump_distance > threshold:
                jumps.append({
                    'index': i,
                    'from_point': current_line['point_id'],
                    'to_point': next_line['point_id'],
                    'jump_distance': jump_distance,
                    'from_pos': (curr_end_x, curr_end_y),
                    'to_pos': (next_start_x, next_start_y)
                })
        
        if jumps:
            print(f"发现 {len(jumps)} 个大跳跃:")
            for jump in jumps:
                print(f"  跳跃: 点{jump['from_point']} -> 点{jump['to_point']}")
                print(f"    从: ({jump['from_pos'][0]:.2f}, {jump['from_pos'][1]:.2f})")
                print(f"    到: ({jump['to_pos'][0]:.2f}, {jump['to_pos'][1]:.2f})")
                print(f"    距离: {jump['jump_distance']:.2f} 像素")
        else:
            print("未发现大跳跃")
        
        return jumps
    
    def analyze_line_directions(self, stroke_id: int):
        """分析线段方向变化"""
        stroke = self.find_stroke(stroke_id)
        if not stroke:
            return
        
        lines = stroke.get('lines', [])
        print(f"\n=== Stroke {stroke_id} 方向变化分析 ===")
        
        directions = []
        sharp_turns = []
        
        for line in lines:
            start_x = line['start_x']
            start_y = line['start_y']
            stop_x = line['stop_x']
            stop_y = line['stop_y']
            
            # 计算方向角度
            dx = stop_x - start_x
            dy = stop_y - start_y
            angle = math.atan2(dy, dx) * 180 / math.pi
            directions.append(angle)
        
        # 检查急转弯
        for i in range(len(directions) - 1):
            angle_diff = abs(directions[i+1] - directions[i])
            if angle_diff > 180:
                angle_diff = 360 - angle_diff
            
            if angle_diff > 90:  # 急转弯阈值
                sharp_turns.append({
                    'index': i,
                    'from_angle': directions[i],
                    'to_angle': directions[i+1],
                    'angle_change': angle_diff
                })
        
        if sharp_turns:
            print(f"发现 {len(sharp_turns)} 个急转弯:")
            for turn in sharp_turns:
                print(f"  急转弯: 线段{turn['index']} -> 线段{turn['index']+1}")
                print(f"    角度变化: {turn['from_angle']:.1f}° -> {turn['to_angle']:.1f}°")
                print(f"    变化幅度: {turn['angle_change']:.1f}°")
        else:
            print("未发现急转弯")
        
        return sharp_turns
    
    def export_stroke_data(self, stroke_id: int, output_file: str):
        """导出stroke数据用于详细分析"""
        stroke = self.find_stroke(stroke_id)
        if not stroke:
            return
        
        lines = stroke.get('lines', [])
        
        # 创建详细的分析数据
        analysis_data = {
            'stroke_info': {
                'stroke_id': stroke_id,
                'total_lines': len(lines),
                'start_timestamp': stroke.get('start_timestamp'),
                'end_timestamp': stroke.get('end_timestamp')
            },
            'lines': [],
            'connectivity_issues': [],
            'coordinate_analysis': {
                'x_range': [float('inf'), float('-inf')],
                'y_range': [float('inf'), float('-inf')],
                'total_length': 0
            }
        }
        
        total_length = 0
        
        for i, line in enumerate(lines):
            start_x = line['start_x']
            start_y = line['start_y']
            stop_x = line['stop_x']
            stop_y = line['stop_y']
            
            # 计算线段长度
            length = math.sqrt((stop_x - start_x) ** 2 + (stop_y - start_y) ** 2)
            total_length += length
            
            # 更新坐标范围
            analysis_data['coordinate_analysis']['x_range'][0] = min(
                analysis_data['coordinate_analysis']['x_range'][0], start_x, stop_x)
            analysis_data['coordinate_analysis']['x_range'][1] = max(
                analysis_data['coordinate_analysis']['x_range'][1], start_x, stop_x)
            analysis_data['coordinate_analysis']['y_range'][0] = min(
                analysis_data['coordinate_analysis']['y_range'][0], start_y, stop_y)
            analysis_data['coordinate_analysis']['y_range'][1] = max(
                analysis_data['coordinate_analysis']['y_range'][1], start_y, stop_y)
            
            line_data = {
                'index': i,
                'point_id': line['point_id'],
                'start': [start_x, start_y],
                'end': [stop_x, stop_y],
                'length': length,
                'stroke_width': line.get('stroke_width', 1.0),
                'timestamp': line.get('timestamp')
            }
            
            # 检查与下一线段的连接
            if i < len(lines) - 1:
                next_line = lines[i + 1]
                next_start_x = next_line['start_x']
                next_start_y = next_line['start_y']
                
                gap = math.sqrt((next_start_x - stop_x) ** 2 + (next_start_y - stop_y) ** 2)
                line_data['gap_to_next'] = gap
                
                if gap > 0:
                    analysis_data['connectivity_issues'].append({
                        'from_line': i,
                        'to_line': i + 1,
                        'gap_distance': gap,
                        'from_end': [stop_x, stop_y],
                        'to_start': [next_start_x, next_start_y]
                    })
            
            analysis_data['lines'].append(line_data)
        
        analysis_data['coordinate_analysis']['total_length'] = total_length
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_data, f, indent=2, ensure_ascii=False)
            print(f"✓ Stroke {stroke_id} 详细数据已导出到: {output_file}")
        except Exception as e:
            print(f"✗ 导出失败: {e}")

def main():
    """主函数"""
    print("=" * 70)
    print("Stroke 分叉问题分析工具")
    print("=" * 70)
    
    # 分析参数
    json_file = 'pen_drawing_1753348768.json'
    target_stroke_id = 19
    
    # 创建分析器
    analyzer = StrokeBranchingAnalyzer()
    
    # 加载数据
    if not analyzer.load_data(json_file):
        return 1
    
    print(f"\n目标分析: Stroke {target_stroke_id}")
    
    # 执行各种分析
    disconnections = analyzer.analyze_stroke_connectivity(target_stroke_id)
    jumps = analyzer.analyze_coordinate_jumps(target_stroke_id, threshold=5.0)
    turns = analyzer.analyze_line_directions(target_stroke_id)
    
    # 导出详细数据
    analyzer.export_stroke_data(target_stroke_id, f'stroke_{target_stroke_id}_analysis.json')
    
    # 总结分析结果
    print(f"\n=== 分叉原因分析总结 ===")
    
    if disconnections:
        print(f"🔍 发现 {len(disconnections)} 个连接断点")
        print("   可能原因: 线段之间存在间隙，导致渲染时出现分叉")
    
    if jumps:
        print(f"🔍 发现 {len(jumps)} 个坐标跳跃")
        print("   可能原因: 坐标突然跳跃，可能是数据采集问题或手势中断")
    
    if turns:
        print(f"🔍 发现 {len(turns)} 个急转弯")
        print("   可能原因: 方向急剧变化，可能导致视觉上的分叉效果")
    
    if not disconnections and not jumps and not turns:
        print("🤔 未发现明显的数据问题")
        print("   分叉可能由以下原因造成:")
        print("   1. 渲染算法问题")
        print("   2. 线宽变化导致的视觉效果")
        print("   3. 抗锯齿或插值算法的影响")
    
    print(f"\n" + "=" * 70)
    print("分析完成！")
    print("=" * 70)
    
    return 0

if __name__ == '__main__':
    exit(main())
