#!/usr/bin/env python3
"""
测试固定画布尺寸1600x2560的功能
"""

import json
import os
import sys
from DrawingRenderer import DrawingRenderer

def create_test_data():
    """创建测试绘制数据"""
    test_data = {
        "capture_info": {
            "timestamp": "2024-07-23T18:00:00.000000",
            "total_strokes": 3,
            "total_lines": 6,
            "total_bitmaps": 0
        },
        "strokes": [
            {
                "stroke_id": 1,
                "start_timestamp": 1690123456789,
                "end_timestamp": 1690123457000,
                "type": "stroke",
                "lines": [
                    {
                        "point_id": 1,
                        "start_x": 100.0,
                        "start_y": 100.0,
                        "stop_x": 200.0,
                        "stop_y": 100.0,
                        "stroke_width": 5.0,
                        "color": -16777216,  # 黑色
                        "alpha": 255,
                        "timestamp": 1690123456789
                    },
                    {
                        "point_id": 2,
                        "start_x": 200.0,
                        "start_y": 100.0,
                        "stop_x": 200.0,
                        "stop_y": 200.0,
                        "stroke_width": 5.0,
                        "color": -16777216,
                        "alpha": 255,
                        "timestamp": 1690123456800
                    }
                ],
                "paths": []
            },
            {
                "stroke_id": 2,
                "start_timestamp": 1690123458000,
                "end_timestamp": 1690123459000,
                "type": "stroke",
                "lines": [
                    {
                        "point_id": 1,
                        "start_x": 800.0,
                        "start_y": 1280.0,  # 画布中心
                        "stop_x": 900.0,
                        "stop_y": 1280.0,
                        "stroke_width": 8.0,
                        "color": -65536,  # 红色
                        "alpha": 255,
                        "timestamp": 1690123458000
                    },
                    {
                        "point_id": 2,
                        "start_x": 900.0,
                        "start_y": 1280.0,
                        "stop_x": 850.0,
                        "stop_y": 1380.0,
                        "stroke_width": 8.0,
                        "color": -65536,
                        "alpha": 255,
                        "timestamp": 1690123458100
                    }
                ],
                "paths": []
            },
            {
                "stroke_id": 3,
                "start_timestamp": 1690123460000,
                "end_timestamp": 1690123461000,
                "type": "stroke",
                "lines": [
                    {
                        "point_id": 1,
                        "start_x": 1400.0,
                        "start_y": 2400.0,  # 接近底部
                        "stop_x": 1500.0,
                        "stop_y": 2400.0,
                        "stroke_width": 3.0,
                        "color": -16711936,  # 绿色
                        "alpha": 255,
                        "timestamp": 1690123460000
                    },
                    {
                        "point_id": 2,
                        "start_x": 1500.0,
                        "start_y": 2400.0,
                        "stop_x": 1450.0,
                        "stop_y": 2500.0,
                        "stroke_width": 3.0,
                        "color": -16711936,
                        "alpha": 255,
                        "timestamp": 1690123460100
                    }
                ],
                "paths": []
            }
        ]
    }
    return test_data

def test_fixed_canvas_size():
    """测试固定画布尺寸功能"""
    print("Testing fixed canvas size 1600x2560...")
    
    # 创建测试数据
    test_data = create_test_data()
    test_file = "test_canvas_data.json"
    
    # 保存测试数据
    with open(test_file, 'w') as f:
        json.dump(test_data, f, indent=2)
    
    print(f"Created test data file: {test_file}")
    
    # 测试默认渲染器（应该使用1600x2560）
    renderer = DrawingRenderer()
    print(f"Default renderer canvas size: {renderer.canvas_width}x{renderer.canvas_height}")
    
    # 验证尺寸
    assert renderer.canvas_width == 1600, f"Expected width 1600, got {renderer.canvas_width}"
    assert renderer.canvas_height == 2560, f"Expected height 2560, got {renderer.canvas_height}"
    
    # 渲染测试图像
    output_file = "test_canvas_output.png"
    rendered_file = renderer.render_from_file(test_file, output_file)
    
    print(f"Rendered test image: {rendered_file}")
    
    # 创建分析图像
    analysis_file = "test_canvas_analysis.png"
    analysis_rendered = renderer.create_analysis_image(test_data, analysis_file)
    
    print(f"Created analysis image: {analysis_rendered}")
    
    # 验证文件存在
    assert os.path.exists(rendered_file), f"Rendered file not found: {rendered_file}"
    assert os.path.exists(analysis_rendered), f"Analysis file not found: {analysis_rendered}"
    
    print("✓ All tests passed!")
    
    # 清理测试文件
    cleanup_files = [test_file, rendered_file, analysis_rendered]
    for file in cleanup_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"Cleaned up: {file}")

def test_coordinate_bounds():
    """测试坐标边界处理"""
    print("\nTesting coordinate bounds...")
    
    # 创建包含边界坐标的测试数据
    boundary_data = {
        "capture_info": {
            "timestamp": "2024-07-23T18:00:00.000000",
            "total_strokes": 1,
            "total_lines": 4,
            "total_bitmaps": 0
        },
        "strokes": [
            {
                "stroke_id": 1,
                "start_timestamp": 1690123456789,
                "end_timestamp": 1690123457000,
                "type": "stroke",
                "lines": [
                    # 左上角
                    {
                        "point_id": 1,
                        "start_x": 0.0,
                        "start_y": 0.0,
                        "stop_x": 50.0,
                        "stop_y": 50.0,
                        "stroke_width": 2.0,
                        "color": -16777216,
                        "alpha": 255,
                        "timestamp": 1690123456789
                    },
                    # 右上角
                    {
                        "point_id": 2,
                        "start_x": 1550.0,
                        "start_y": 0.0,
                        "stop_x": 1599.0,
                        "stop_y": 50.0,
                        "stroke_width": 2.0,
                        "color": -65536,
                        "alpha": 255,
                        "timestamp": 1690123456800
                    },
                    # 左下角
                    {
                        "point_id": 3,
                        "start_x": 0.0,
                        "start_y": 2510.0,
                        "stop_x": 50.0,
                        "stop_y": 2559.0,
                        "stroke_width": 2.0,
                        "color": -16711936,
                        "alpha": 255,
                        "timestamp": 1690123456900
                    },
                    # 右下角
                    {
                        "point_id": 4,
                        "start_x": 1550.0,
                        "start_y": 2510.0,
                        "stop_x": 1599.0,
                        "stop_y": 2559.0,
                        "stroke_width": 2.0,
                        "color": -256,  # 黄色
                        "alpha": 255,
                        "timestamp": 1690123457000
                    }
                ],
                "paths": []
            }
        ]
    }
    
    # 测试边界坐标渲染
    renderer = DrawingRenderer()
    boundary_file = "test_boundary_output.png"
    
    try:
        image = renderer.render_drawing_data(boundary_data)
        image.save(boundary_file)
        print(f"✓ Boundary coordinates rendered successfully: {boundary_file}")
        
        # 清理
        if os.path.exists(boundary_file):
            os.remove(boundary_file)
            
    except Exception as e:
        print(f"✗ Error rendering boundary coordinates: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("=" * 50)
    print("Fixed Canvas Size Test (1600x2560)")
    print("=" * 50)
    
    try:
        # 测试固定画布尺寸
        test_fixed_canvas_size()
        
        # 测试坐标边界
        test_coordinate_bounds()
        
        print("\n" + "=" * 50)
        print("All tests completed successfully!")
        print("Canvas size is properly fixed at 1600x2560")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
