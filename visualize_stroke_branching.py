#!/usr/bin/env python3
"""
可视化stroke分叉问题
创建详细的分析图像来展示急转弯和分叉原因
"""

import json
import math
from PIL import Image, ImageDraw, ImageFont
from typing import List, Dict, Tuple

class StrokeBranchingVisualizer:
    def __init__(self, canvas_width: int = 1200, canvas_height: int = 800):
        self.canvas_width = canvas_width
        self.canvas_height = canvas_height
        self.stroke_data = None
    
    def load_stroke_analysis(self, json_file: str):
        """加载stroke分析数据"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                self.stroke_data = json.load(f)
            print(f"✓ 加载stroke分析数据: {len(self.stroke_data['lines'])} 条线段")
            return True
        except Exception as e:
            print(f"✗ 加载分析数据失败: {e}")
            return False
    
    def calculate_bounds(self):
        """计算坐标边界"""
        if not self.stroke_data:
            return None
        
        lines = self.stroke_data['lines']
        min_x = min_y = float('inf')
        max_x = max_y = float('-inf')
        
        for line in lines:
            start_x, start_y = line['start']
            end_x, end_y = line['end']
            
            min_x = min(min_x, start_x, end_x)
            max_x = max(max_x, start_x, end_x)
            min_y = min(min_y, start_y, end_y)
            max_y = max(max_y, start_y, end_y)
        
        return {
            'min_x': min_x, 'max_x': max_x,
            'min_y': min_y, 'max_y': max_y,
            'width': max_x - min_x,
            'height': max_y - min_y
        }
    
    def transform_coordinates(self, x: float, y: float, bounds: dict, margin: int = 50):
        """将原始坐标转换为画布坐标"""
        # 计算缩放比例
        available_width = self.canvas_width - 2 * margin
        available_height = self.canvas_height - 2 * margin
        
        scale_x = available_width / bounds['width'] if bounds['width'] > 0 else 1
        scale_y = available_height / bounds['height'] if bounds['height'] > 0 else 1
        scale = min(scale_x, scale_y)
        
        # 转换坐标
        canvas_x = margin + (x - bounds['min_x']) * scale
        canvas_y = margin + (y - bounds['min_y']) * scale
        
        return int(canvas_x), int(canvas_y), scale
    
    def find_sharp_turns(self, angle_threshold: float = 90.0):
        """找出急转弯位置"""
        if not self.stroke_data:
            return []
        
        lines = self.stroke_data['lines']
        sharp_turns = []
        
        for i in range(len(lines) - 1):
            current_line = lines[i]
            next_line = lines[i + 1]
            
            # 计算当前线段角度
            curr_start = current_line['start']
            curr_end = current_line['end']
            curr_angle = math.atan2(curr_end[1] - curr_start[1], curr_end[0] - curr_start[0])
            
            # 计算下一线段角度
            next_start = next_line['start']
            next_end = next_line['end']
            next_angle = math.atan2(next_end[1] - next_start[1], next_end[0] - next_start[0])
            
            # 计算角度差
            angle_diff = abs(next_angle - curr_angle) * 180 / math.pi
            if angle_diff > 180:
                angle_diff = 360 - angle_diff
            
            if angle_diff > angle_threshold:
                sharp_turns.append({
                    'index': i,
                    'position': curr_end,
                    'angle_change': angle_diff,
                    'from_angle': curr_angle * 180 / math.pi,
                    'to_angle': next_angle * 180 / math.pi
                })
        
        return sharp_turns
    
    def create_analysis_visualization(self, output_file: str):
        """创建分析可视化图像"""
        if not self.stroke_data:
            print("✗ 没有stroke数据")
            return
        
        # 计算边界
        bounds = self.calculate_bounds()
        if not bounds:
            print("✗ 无法计算坐标边界")
            return
        
        # 找出急转弯
        sharp_turns = self.find_sharp_turns(angle_threshold=90.0)
        
        # 创建画布
        image = Image.new('RGB', (self.canvas_width, self.canvas_height), (255, 255, 255))
        draw = ImageDraw.Draw(image)
        
        # 尝试加载字体
        try:
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 12)
            title_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 16)
        except:
            font = ImageFont.load_default()
            title_font = ImageFont.load_default()
        
        # 绘制标题
        stroke_id = self.stroke_data['stroke_info']['stroke_id']
        title = f"Stroke {stroke_id} 分叉分析 - {len(self.stroke_data['lines'])} 线段, {len(sharp_turns)} 急转弯"
        draw.text((10, 10), title, fill=(0, 0, 0), font=title_font)
        
        # 绘制线段
        lines = self.stroke_data['lines']
        for i, line in enumerate(lines):
            start_x, start_y = line['start']
            end_x, end_y = line['end']
            
            # 转换坐标
            canvas_start_x, canvas_start_y, scale = self.transform_coordinates(start_x, start_y, bounds)
            canvas_end_x, canvas_end_y, _ = self.transform_coordinates(end_x, end_y, bounds)
            
            # 根据是否是急转弯附近选择颜色
            is_near_turn = any(abs(turn['index'] - i) <= 1 for turn in sharp_turns)
            line_color = (255, 0, 0) if is_near_turn else (0, 0, 0)  # 红色表示急转弯附近
            
            # 绘制线段
            draw.line([(canvas_start_x, canvas_start_y), (canvas_end_x, canvas_end_y)], 
                     fill=line_color, width=2)
        
        # 标记急转弯位置
        for i, turn in enumerate(sharp_turns):
            pos_x, pos_y = turn['position']
            canvas_x, canvas_y, _ = self.transform_coordinates(pos_x, pos_y, bounds)
            
            # 绘制急转弯标记
            radius = 8
            draw.ellipse([canvas_x - radius, canvas_y - radius, 
                         canvas_x + radius, canvas_y + radius], 
                        fill=(255, 0, 0), outline=(0, 0, 0), width=2)
            
            # 添加标签
            label = f"T{i+1}\n{turn['angle_change']:.0f}°"
            draw.text((canvas_x + radius + 5, canvas_y - 10), label, 
                     fill=(255, 0, 0), font=font)
        
        # 添加图例
        legend_y = self.canvas_height - 100
        draw.text((10, legend_y), "图例:", fill=(0, 0, 0), font=title_font)
        draw.text((10, legend_y + 20), "● 黑色线段: 正常线段", fill=(0, 0, 0), font=font)
        draw.text((10, legend_y + 35), "● 红色线段: 急转弯附近", fill=(255, 0, 0), font=font)
        draw.text((10, legend_y + 50), "● 红色圆圈: 急转弯位置", fill=(255, 0, 0), font=font)
        
        # 添加统计信息
        stats_x = self.canvas_width - 300
        draw.text((stats_x, legend_y), "统计信息:", fill=(0, 0, 0), font=title_font)
        draw.text((stats_x, legend_y + 20), f"总线段数: {len(lines)}", fill=(0, 0, 0), font=font)
        draw.text((stats_x, legend_y + 35), f"急转弯数: {len(sharp_turns)}", fill=(0, 0, 0), font=font)
        draw.text((stats_x, legend_y + 50), f"坐标范围: {bounds['width']:.0f}x{bounds['height']:.0f}", 
                 fill=(0, 0, 0), font=font)
        draw.text((stats_x, legend_y + 65), f"缩放比例: {scale:.3f}", fill=(0, 0, 0), font=font)
        
        # 保存图像
        image.save(output_file, 'PNG')
        print(f"✓ 分析可视化图像已保存到: {output_file}")
        
        return sharp_turns
    
    def create_detailed_turn_analysis(self, output_file: str):
        """创建详细的急转弯分析图"""
        if not self.stroke_data:
            return
        
        sharp_turns = self.find_sharp_turns(angle_threshold=90.0)
        if not sharp_turns:
            print("没有发现急转弯")
            return
        
        # 为每个急转弯创建详细分析
        lines = self.stroke_data['lines']
        
        # 创建更大的画布用于详细分析
        detail_width = 400
        detail_height = 300
        cols = 2
        rows = (len(sharp_turns) + cols - 1) // cols
        
        total_width = cols * detail_width + 50
        total_height = rows * detail_height + 100
        
        image = Image.new('RGB', (total_width, total_height), (255, 255, 255))
        draw = ImageDraw.Draw(image)
        
        try:
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 10)
            title_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 14)
        except:
            font = ImageFont.load_default()
            title_font = ImageFont.load_default()
        
        # 绘制总标题
        stroke_id = self.stroke_data['stroke_info']['stroke_id']
        title = f"Stroke {stroke_id} 急转弯详细分析"
        draw.text((10, 10), title, fill=(0, 0, 0), font=title_font)
        
        # 为每个急转弯创建详细视图
        for i, turn in enumerate(sharp_turns):
            row = i // cols
            col = i % cols
            
            # 计算子图位置
            sub_x = col * detail_width + 25
            sub_y = row * detail_height + 50
            
            # 获取急转弯附近的线段
            turn_index = turn['index']
            context_range = 5  # 前后各5个线段
            start_idx = max(0, turn_index - context_range)
            end_idx = min(len(lines), turn_index + context_range + 2)
            
            context_lines = lines[start_idx:end_idx]
            
            # 计算局部边界
            local_bounds = self.calculate_local_bounds(context_lines)
            
            # 绘制子图边框
            draw.rectangle([sub_x, sub_y, sub_x + detail_width - 50, sub_y + detail_height - 50], 
                          outline=(0, 0, 0), width=1)
            
            # 绘制子图标题
            sub_title = f"急转弯 #{i+1} (线段{turn_index})"
            draw.text((sub_x + 5, sub_y - 20), sub_title, fill=(0, 0, 0), font=font)
            
            # 绘制局部线段
            for j, line in enumerate(context_lines):
                actual_idx = start_idx + j
                start_x, start_y = line['start']
                end_x, end_y = line['end']
                
                # 转换到子图坐标
                local_start_x, local_start_y = self.transform_to_subimage(
                    start_x, start_y, local_bounds, sub_x + 5, sub_y + 5, 
                    detail_width - 60, detail_height - 60)
                local_end_x, local_end_y = self.transform_to_subimage(
                    end_x, end_y, local_bounds, sub_x + 5, sub_y + 5, 
                    detail_width - 60, detail_height - 60)
                
                # 选择颜色
                if actual_idx == turn_index or actual_idx == turn_index + 1:
                    color = (255, 0, 0)  # 急转弯的两个线段用红色
                    width = 3
                else:
                    color = (100, 100, 100)  # 其他线段用灰色
                    width = 1
                
                draw.line([(local_start_x, local_start_y), (local_end_x, local_end_y)], 
                         fill=color, width=width)
            
            # 添加角度信息
            angle_text = f"角度变化: {turn['angle_change']:.1f}°"
            draw.text((sub_x + 5, sub_y + detail_height - 45), angle_text, 
                     fill=(255, 0, 0), font=font)
        
        # 保存详细分析图
        image.save(output_file, 'PNG')
        print(f"✓ 详细急转弯分析图已保存到: {output_file}")
    
    def calculate_local_bounds(self, lines: List[Dict]):
        """计算局部线段的边界"""
        min_x = min_y = float('inf')
        max_x = max_y = float('-inf')
        
        for line in lines:
            start_x, start_y = line['start']
            end_x, end_y = line['end']
            
            min_x = min(min_x, start_x, end_x)
            max_x = max(max_x, start_x, end_x)
            min_y = min(min_y, start_y, end_y)
            max_y = max(max_y, start_y, end_y)
        
        return {
            'min_x': min_x, 'max_x': max_x,
            'min_y': min_y, 'max_y': max_y,
            'width': max_x - min_x,
            'height': max_y - min_y
        }
    
    def transform_to_subimage(self, x: float, y: float, bounds: dict, 
                             sub_x: int, sub_y: int, sub_width: int, sub_height: int):
        """转换坐标到子图"""
        if bounds['width'] == 0 or bounds['height'] == 0:
            return sub_x + sub_width // 2, sub_y + sub_height // 2
        
        scale_x = sub_width / bounds['width']
        scale_y = sub_height / bounds['height']
        scale = min(scale_x, scale_y)
        
        local_x = sub_x + (x - bounds['min_x']) * scale
        local_y = sub_y + (y - bounds['min_y']) * scale
        
        return int(local_x), int(local_y)

def main():
    """主函数"""
    print("=" * 70)
    print("Stroke 分叉可视化分析工具")
    print("=" * 70)
    
    # 创建可视化器
    visualizer = StrokeBranchingVisualizer()
    
    # 加载分析数据
    if not visualizer.load_stroke_analysis('stroke_7_analysis.json'):
        return 1

    # 创建分析可视化
    sharp_turns = visualizer.create_analysis_visualization('stroke_7_branching_analysis.png')

    # 创建详细急转弯分析
    visualizer.create_detailed_turn_analysis('stroke_7_detailed_turns.png')
    
    # 输出分析结论
    print(f"\n=== 分叉原因分析结论 ===")
    print(f"发现 {len(sharp_turns)} 个急转弯:")
    
    for i, turn in enumerate(sharp_turns):
        print(f"  急转弯 #{i+1}: 线段{turn['index']} -> 线段{turn['index']+1}")
        print(f"    位置: ({turn['position'][0]:.1f}, {turn['position'][1]:.1f})")
        print(f"    角度变化: {turn['angle_change']:.1f}°")
        print(f"    从 {turn['from_angle']:.1f}° 到 {turn['to_angle']:.1f}°")
    
    print(f"\n💡 分叉原因:")
    print(f"   Stroke 19 出现分叉是因为存在 {len(sharp_turns)} 个急转弯")
    print(f"   这些急转弯导致线段方向突然改变，在渲染时可能产生视觉上的分叉效果")
    print(f"   特别是当线宽较大时，急转弯处的线段重叠会更加明显")
    
    print(f"\n" + "=" * 70)
    print("可视化分析完成！")
    print("=" * 70)
    
    return 0

if __name__ == '__main__':
    exit(main())
