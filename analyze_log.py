#!/usr/bin/env python3
"""
Android日志分析工具
专门分析log.txt文件中的PenDemo应用日志
"""

import re
import json
from datetime import datetime
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Any

class LogAnalyzer:
    def __init__(self, log_file: str):
        self.log_file = log_file
        self.lines = []
        self.pen_logs = []
        self.draw_logs = []
        self.strokes = defaultdict(list)
        self.statistics = {}
        
    def load_log(self):
        """加载日志文件"""
        try:
            with open(self.log_file, 'r', encoding='utf-8') as f:
                self.lines = f.readlines()
            print(f"✓ 成功加载 {len(self.lines)} 行日志")
            return True
        except Exception as e:
            print(f"✗ 加载日志失败: {e}")
            return False
    
    def extract_pen_logs(self):
        """提取PenDemo相关日志"""
        pen_pattern = re.compile(r'com\.onyx\.demo\.pen')
        draw_pattern = re.compile(r'PenDrawLog|drawLine|STROKE_|DRAW_LINE')
        
        for i, line in enumerate(self.lines):
            if pen_pattern.search(line):
                self.pen_logs.append({
                    'line_num': i + 1,
                    'content': line.strip(),
                    'timestamp': self._extract_timestamp(line)
                })
            
            if draw_pattern.search(line):
                self.draw_logs.append({
                    'line_num': i + 1,
                    'content': line.strip(),
                    'timestamp': self._extract_timestamp(line)
                })
        
        print(f"✓ 提取到 {len(self.pen_logs)} 条PenDemo日志")
        print(f"✓ 提取到 {len(self.draw_logs)} 条绘制日志")
    
    def _extract_timestamp(self, line: str) -> str:
        """提取时间戳"""
        match = re.search(r'(\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})', line)
        return match.group(1) if match else ""
    
    def analyze_drawing_performance(self):
        """分析绘制性能"""
        stroke_starts = []
        draw_lines = []
        
        for log in self.draw_logs:
            content = log['content']
            if 'STROKE_START' in content:
                match = re.search(r'strokeId=(\d+)\|timestamp=(\d+)', content)
                if match:
                    stroke_starts.append({
                        'stroke_id': int(match.group(1)),
                        'timestamp': int(match.group(2)),
                        'line_num': log['line_num']
                    })
            
            elif 'DRAW_LINE' in content:
                match = re.search(r'strokeId=(\d+)\|pointId=(\d+)', content)
                if match:
                    draw_lines.append({
                        'stroke_id': int(match.group(1)),
                        'point_id': int(match.group(2)),
                        'line_num': log['line_num']
                    })
        
        # 按stroke分组
        for line in draw_lines:
            stroke_id = line['stroke_id']
            self.strokes[stroke_id].append(line)
        
        self.statistics['stroke_starts'] = len(stroke_starts)
        self.statistics['total_draw_lines'] = len(draw_lines)
        self.statistics['unique_strokes'] = len(self.strokes)
        
        print(f"\n=== 绘制性能分析 ===")
        print(f"Stroke开始事件: {self.statistics['stroke_starts']}")
        print(f"总绘制线段: {self.statistics['total_draw_lines']}")
        print(f"唯一Stroke数: {self.statistics['unique_strokes']}")
        
        # 分析每个stroke的线段数
        stroke_line_counts = [len(lines) for lines in self.strokes.values()]
        if stroke_line_counts:
            print(f"平均每个Stroke线段数: {sum(stroke_line_counts) / len(stroke_line_counts):.1f}")
            print(f"最大Stroke线段数: {max(stroke_line_counts)}")
            print(f"最小Stroke线段数: {min(stroke_line_counts)}")
    
    def analyze_app_lifecycle(self):
        """分析应用生命周期"""
        lifecycle_events = []
        
        for log in self.pen_logs:
            content = log['content']
            if 'PenActivity' in content:
                if 'getVisibleTasks' in content:
                    lifecycle_events.append({
                        'event': 'activity_visible',
                        'timestamp': log['timestamp'],
                        'line_num': log['line_num']
                    })
                elif 'ProfileInstaller' in content:
                    lifecycle_events.append({
                        'event': 'profile_install',
                        'timestamp': log['timestamp'],
                        'line_num': log['line_num']
                    })
        
        print(f"\n=== 应用生命周期分析 ===")
        print(f"Activity可见事件: {len([e for e in lifecycle_events if e['event'] == 'activity_visible'])}")
        print(f"Profile安装事件: {len([e for e in lifecycle_events if e['event'] == 'profile_install'])}")
    
    def analyze_system_performance(self):
        """分析系统性能相关日志"""
        performance_logs = []
        
        for line in self.lines:
            if any(keyword in line for keyword in ['Benchmark', 'onDraw', 'onTouchEvent']):
                performance_logs.append(line.strip())
        
        # 提取onDraw时间
        draw_times = []
        for log in performance_logs:
            if 'onDraw finished' in log:
                match = re.search(r'(\d+)ms', log)
                if match:
                    draw_times.append(int(match.group(1)))
        
        print(f"\n=== 系统性能分析 ===")
        print(f"性能相关日志: {len(performance_logs)}")
        if draw_times:
            print(f"onDraw事件数: {len(draw_times)}")
            print(f"平均绘制时间: {sum(draw_times) / len(draw_times):.1f}ms")
            print(f"最大绘制时间: {max(draw_times)}ms")
            print(f"最小绘制时间: {min(draw_times)}ms")
    
    def analyze_error_warnings(self):
        """分析错误和警告"""
        errors = []
        warnings = []
        
        for i, line in enumerate(self.lines):
            if ' E ' in line:
                errors.append({
                    'line_num': i + 1,
                    'content': line.strip(),
                    'timestamp': self._extract_timestamp(line)
                })
            elif ' W ' in line:
                warnings.append({
                    'line_num': i + 1,
                    'content': line.strip(),
                    'timestamp': self._extract_timestamp(line)
                })
        
        print(f"\n=== 错误和警告分析 ===")
        print(f"错误日志: {len(errors)}")
        print(f"警告日志: {len(warnings)}")
        
        # 显示前5个错误
        if errors:
            print("\n前5个错误:")
            for error in errors[:5]:
                print(f"  行{error['line_num']}: {error['content'][:100]}...")
    
    def analyze_time_distribution(self):
        """分析时间分布"""
        timestamps = []
        
        for log in self.draw_logs:
            if log['timestamp']:
                timestamps.append(log['timestamp'])
        
        if timestamps:
            print(f"\n=== 时间分布分析 ===")
            print(f"绘制日志时间范围: {min(timestamps)} - {max(timestamps)}")
            
            # 按秒分组统计
            second_counts = Counter()
            for ts in timestamps:
                second = ts[:8]  # 取到秒
                second_counts[second] += 1
            
            if second_counts:
                max_second = max(second_counts.values())
                avg_second = sum(second_counts.values()) / len(second_counts)
                print(f"最高每秒日志数: {max_second}")
                print(f"平均每秒日志数: {avg_second:.1f}")
    
    def generate_report(self, output_file: str = "log_analysis_report.json"):
        """生成分析报告"""
        report = {
            'analysis_time': datetime.now().isoformat(),
            'log_file': self.log_file,
            'total_lines': len(self.lines),
            'pen_logs_count': len(self.pen_logs),
            'draw_logs_count': len(self.draw_logs),
            'statistics': self.statistics,
            'stroke_details': {
                str(k): len(v) for k, v in self.strokes.items()
            }
        }
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"\n✓ 分析报告已保存到: {output_file}")
        except Exception as e:
            print(f"✗ 保存报告失败: {e}")
    
    def print_summary(self):
        """打印分析摘要"""
        print(f"\n" + "=" * 60)
        print("LOG.TXT 分析摘要")
        print("=" * 60)
        print(f"日志文件: {self.log_file}")
        print(f"总行数: {len(self.lines)}")
        print(f"PenDemo相关日志: {len(self.pen_logs)}")
        print(f"绘制相关日志: {len(self.draw_logs)}")
        
        if self.strokes:
            stroke_ids = sorted(self.strokes.keys())
            print(f"Stroke ID范围: {min(stroke_ids)} - {max(stroke_ids)}")
            print(f"总Stroke数: {len(stroke_ids)}")
            
            # 显示前10个stroke的详情
            print(f"\n前10个Stroke详情:")
            for stroke_id in stroke_ids[:10]:
                line_count = len(self.strokes[stroke_id])
                print(f"  Stroke {stroke_id}: {line_count} 条线段")

def main():
    """主函数"""
    print("=" * 60)
    print("Android日志分析工具")
    print("=" * 60)
    
    # 创建分析器
    analyzer = LogAnalyzer('log.txt')
    
    # 加载日志
    if not analyzer.load_log():
        return 1
    
    # 执行分析
    analyzer.extract_pen_logs()
    analyzer.analyze_drawing_performance()
    analyzer.analyze_app_lifecycle()
    analyzer.analyze_system_performance()
    analyzer.analyze_error_warnings()
    analyzer.analyze_time_distribution()
    
    # 生成报告
    analyzer.generate_report()
    
    # 打印摘要
    analyzer.print_summary()
    
    print(f"\n" + "=" * 60)
    print("分析完成！")
    print("=" * 60)
    
    return 0

if __name__ == '__main__':
    exit(main())
