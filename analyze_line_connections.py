#!/usr/bin/env python3
"""
分析line.json中首尾不相连的结点
找出线段之间的断点和连接问题
"""

import json
import math
from typing import List, Dict, Tuple, Optional

class LineConnectionAnalyzer:
    def __init__(self, tolerance: float = 0.1):
        """
        初始化分析器
        
        Args:
            tolerance: 坐标匹配的容差值，默认0.1像素
        """
        self.tolerance = tolerance
        self.lines = []
        self.disconnections = []
        self.statistics = {}
    
    def load_lines(self, json_file: str):
        """加载line.json文件"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                self.lines = json.load(f)
            print(f"✓ 成功加载 {len(self.lines)} 条线段")
            return True
        except Exception as e:
            print(f"✗ 加载文件失败: {e}")
            return False
    
    def distance(self, x1: float, y1: float, x2: float, y2: float) -> float:
        """计算两点之间的距离"""
        return math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
    
    def points_match(self, x1: float, y1: float, x2: float, y2: float) -> bool:
        """判断两个点是否匹配（在容差范围内）"""
        return self.distance(x1, y1, x2, y2) <= self.tolerance
    
    def analyze_connections(self):
        """分析线段连接情况"""
        if not self.lines:
            print("✗ 没有线段数据")
            return
        
        print(f"\n开始分析 {len(self.lines)} 条线段的连接情况...")
        print(f"容差设置: {self.tolerance} 像素")
        
        self.disconnections = []
        connected_count = 0
        
        for i in range(len(self.lines) - 1):
            current_line = self.lines[i]
            next_line = self.lines[i + 1]
            
            # 当前线段的终点
            current_end_x = current_line['stop_x']
            current_end_y = current_line['stop_y']
            
            # 下一线段的起点
            next_start_x = next_line['start_x']
            next_start_y = next_line['start_y']
            
            # 检查是否连接
            if self.points_match(current_end_x, current_end_y, next_start_x, next_start_y):
                connected_count += 1
            else:
                # 记录断点
                gap_distance = self.distance(current_end_x, current_end_y, next_start_x, next_start_y)
                disconnection = {
                    'line_index': i,
                    'current_line_id': current_line['point_id'],
                    'next_line_id': next_line['point_id'],
                    'current_end': (current_end_x, current_end_y),
                    'next_start': (next_start_x, next_start_y),
                    'gap_distance': gap_distance,
                    'timestamp_current': current_line.get('timestamp', 0),
                    'timestamp_next': next_line.get('timestamp', 0)
                }
                self.disconnections.append(disconnection)
        
        # 统计信息
        total_connections = len(self.lines) - 1
        disconnected_count = len(self.disconnections)
        
        self.statistics = {
            'total_lines': len(self.lines),
            'total_connections': total_connections,
            'connected_count': connected_count,
            'disconnected_count': disconnected_count,
            'connection_rate': (connected_count / total_connections * 100) if total_connections > 0 else 0
        }
        
        print(f"\n=== 连接分析结果 ===")
        print(f"总线段数: {self.statistics['total_lines']}")
        print(f"总连接点数: {self.statistics['total_connections']}")
        print(f"已连接: {self.statistics['connected_count']}")
        print(f"未连接: {self.statistics['disconnected_count']}")
        print(f"连接率: {self.statistics['connection_rate']:.1f}%")
    
    def print_disconnections(self, max_show: int = 20):
        """打印断点详情"""
        if not self.disconnections:
            print("\n✓ 所有线段都是连续的，没有发现断点！")
            return
        
        print(f"\n=== 发现 {len(self.disconnections)} 个断点 ===")
        
        # 按间距排序，显示最大的断点
        sorted_disconnections = sorted(self.disconnections, key=lambda x: x['gap_distance'], reverse=True)
        
        show_count = min(max_show, len(sorted_disconnections))
        print(f"显示前 {show_count} 个最大断点:")
        
        for i, disc in enumerate(sorted_disconnections[:show_count]):
            print(f"\n断点 #{i+1}:")
            print(f"  位置: 线段 {disc['current_line_id']} -> 线段 {disc['next_line_id']}")
            print(f"  当前线段终点: ({disc['current_end'][0]:.2f}, {disc['current_end'][1]:.2f})")
            print(f"  下一线段起点: ({disc['next_start'][0]:.2f}, {disc['next_start'][1]:.2f})")
            print(f"  间距: {disc['gap_distance']:.2f} 像素")
            
            # 时间戳差异
            if disc['timestamp_current'] and disc['timestamp_next']:
                time_diff = disc['timestamp_next'] - disc['timestamp_current']
                print(f"  时间间隔: {time_diff} ms")
    
    def analyze_gap_distribution(self):
        """分析断点间距分布"""
        if not self.disconnections:
            return
        
        gaps = [d['gap_distance'] for d in self.disconnections]
        
        print(f"\n=== 断点间距分布 ===")
        print(f"最小间距: {min(gaps):.2f} 像素")
        print(f"最大间距: {max(gaps):.2f} 像素")
        print(f"平均间距: {sum(gaps)/len(gaps):.2f} 像素")
        
        # 间距范围统计
        ranges = [
            (0, 1, "微小断点 (0-1px)"),
            (1, 5, "小断点 (1-5px)"),
            (5, 20, "中等断点 (5-20px)"),
            (20, 100, "大断点 (20-100px)"),
            (100, float('inf'), "巨大断点 (>100px)")
        ]
        
        for min_gap, max_gap, label in ranges:
            count = sum(1 for gap in gaps if min_gap <= gap < max_gap)
            if count > 0:
                percentage = count / len(gaps) * 100
                print(f"{label}: {count} 个 ({percentage:.1f}%)")
    
    def find_stroke_boundaries(self):
        """识别可能的笔画边界"""
        if not self.disconnections:
            return
        
        print(f"\n=== 笔画边界分析 ===")
        
        # 大间距的断点可能是笔画边界
        stroke_boundaries = [d for d in self.disconnections if d['gap_distance'] > 10.0]
        
        if stroke_boundaries:
            print(f"发现 {len(stroke_boundaries)} 个可能的笔画边界 (间距 > 10px):")
            for i, boundary in enumerate(stroke_boundaries):
                print(f"  边界 #{i+1}: 线段 {boundary['current_line_id']} -> {boundary['next_line_id']}, "
                      f"间距 {boundary['gap_distance']:.2f}px")
        else:
            print("未发现明显的笔画边界")
    
    def export_disconnections(self, output_file: str):
        """导出断点信息到JSON文件"""
        if not self.disconnections:
            print("没有断点数据可导出")
            return
        
        export_data = {
            'analysis_info': {
                'total_lines': self.statistics['total_lines'],
                'disconnected_count': self.statistics['disconnected_count'],
                'connection_rate': self.statistics['connection_rate'],
                'tolerance': self.tolerance
            },
            'disconnections': self.disconnections
        }
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            print(f"✓ 断点分析结果已导出到: {output_file}")
        except Exception as e:
            print(f"✗ 导出失败: {e}")
    
    def create_visualization_data(self, output_file: str):
        """创建可视化数据文件"""
        if not self.disconnections:
            print("没有断点数据可可视化")
            return
        
        # 创建用于可视化的简化数据
        viz_data = {
            'lines': [],
            'disconnections': [],
            'statistics': self.statistics
        }
        
        # 添加线段数据（简化）
        for line in self.lines:
            viz_data['lines'].append({
                'id': line['point_id'],
                'start': [line['start_x'], line['start_y']],
                'end': [line['stop_x'], line['stop_y']],
                'width': line.get('stroke_width', 1.0)
            })
        
        # 添加断点数据
        for disc in self.disconnections:
            viz_data['disconnections'].append({
                'from_line': disc['current_line_id'],
                'to_line': disc['next_line_id'],
                'gap_start': list(disc['current_end']),
                'gap_end': list(disc['next_start']),
                'distance': disc['gap_distance']
            })
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(viz_data, f, indent=2, ensure_ascii=False)
            print(f"✓ 可视化数据已导出到: {output_file}")
        except Exception as e:
            print(f"✗ 导出可视化数据失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("Line.json 连接性分析工具")
    print("=" * 60)
    
    # 创建分析器
    analyzer = LineConnectionAnalyzer(tolerance=0.1)
    
    # 加载数据
    if not analyzer.load_lines('line.json'):
        return 1
    
    # 执行分析
    analyzer.analyze_connections()
    
    # 显示断点详情
    analyzer.print_disconnections(max_show=10)
    
    # 分析间距分布
    analyzer.analyze_gap_distribution()
    
    # 识别笔画边界
    analyzer.find_stroke_boundaries()
    
    # 导出结果
    analyzer.export_disconnections('line_disconnections.json')
    analyzer.create_visualization_data('line_visualization.json')
    
    print(f"\n" + "=" * 60)
    print("分析完成！")
    print("=" * 60)
    
    return 0

if __name__ == '__main__':
    exit(main())
