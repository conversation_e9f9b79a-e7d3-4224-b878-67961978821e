#!/usr/bin/env python3
"""
绘制数据渲染器
读取捕获的绘制数据JSON文件，重新绘制为位图并保存
"""

import json
import argparse
from PIL import Image, ImageDraw
from typing import List, Dict, Any, Tuple
import colorsys
import os

class DrawingRenderer:
    def __init__(self, canvas_width: int = 1600, canvas_height: int = 2560):
        self.canvas_width = canvas_width
        self.canvas_height = canvas_height
        self.background_color = (255, 255, 255, 255)  # 白色背景
        
    def render_from_file(self, input_file: str, output_file: str = None) -> str:
        """从JSON文件渲染绘制数据"""
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"Input file not found: {input_file}")

        # 读取数据
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 生成输出文件名
        if output_file is None:
            base_name = os.path.splitext(input_file)[0]
            output_file = f"{base_name}_rendered.png"

        # 渲染图像
        image = self.render_drawing_data(data)

        # 保存图像
        image.save(output_file, 'PNG')
        print(f"Rendered image saved to: {output_file}")

        return output_file

    def render_individual_strokes(self, input_file: str, output_dir: str = None) -> List[str]:
        """为每个stroke单独渲染一张位图"""
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"Input file not found: {input_file}")

        # 读取数据
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 创建输出目录
        if output_dir is None:
            base_name = os.path.splitext(input_file)[0]
            output_dir = f"{base_name}_strokes"

        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        strokes = [s for s in data.get('strokes', []) if s.get('type') == 'stroke']
        output_files = []

        print(f"Rendering {len(strokes)} individual strokes to directory: {output_dir}")

        for i, stroke in enumerate(strokes):
            stroke_id = stroke.get('stroke_id', i + 1)

            # 创建只包含当前stroke的数据
            single_stroke_data = {
                'capture_info': {
                    'timestamp': data.get('capture_info', {}).get('timestamp', ''),
                    'total_strokes': 1,
                    'total_lines': len(stroke.get('lines', [])),
                    'total_bitmaps': 0,
                    'stroke_id': stroke_id
                },
                'strokes': [stroke]
            }

            # 渲染单个stroke
            image = self.render_drawing_data(single_stroke_data)

            # 保存图像
            output_file = os.path.join(output_dir, f"stroke_{stroke_id:03d}.png")
            image.save(output_file, 'PNG')
            output_files.append(output_file)

            print(f"Rendered stroke {stroke_id} with {len(stroke.get('lines', []))} lines -> {output_file}")

        return output_files

    def create_stroke_analysis_grid(self, input_file: str, output_file: str = None) -> str:
        """创建显示所有stroke的网格分析图"""
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"Input file not found: {input_file}")

        # 读取数据
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        strokes = [s for s in data.get('strokes', []) if s.get('type') == 'stroke']

        if not strokes:
            raise ValueError("No strokes found in data")

        # 计算网格布局
        import math
        stroke_count = len(strokes)
        cols = math.ceil(math.sqrt(stroke_count))
        rows = math.ceil(stroke_count / cols)

        # 每个stroke的缩略图尺寸
        thumb_width = 200
        thumb_height = 320  # 保持16:25.6比例

        # 创建网格画布
        grid_width = cols * thumb_width + (cols + 1) * 20  # 20px间距
        grid_height = rows * thumb_height + (rows + 1) * 20 + 100  # 额外100px用于标题

        from PIL import Image, ImageDraw, ImageFont
        grid_image = Image.new('RGB', (grid_width, grid_height), (240, 240, 240))
        grid_draw = ImageDraw.Draw(grid_image)

        # 绘制标题
        title = f"Stroke Analysis Grid - {stroke_count} Strokes"
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 24)
        except:
            font = ImageFont.load_default()

        title_bbox = grid_draw.textbbox((0, 0), title, font=font)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (grid_width - title_width) // 2
        grid_draw.text((title_x, 20), title, fill=(0, 0, 0), font=font)

        # 渲染每个stroke的缩略图
        for i, stroke in enumerate(strokes):
            row = i // cols
            col = i % cols

            # 计算位置
            x = 20 + col * (thumb_width + 20)
            y = 80 + row * (thumb_height + 20)

            # 创建单个stroke的数据
            single_stroke_data = {
                'capture_info': {
                    'timestamp': data.get('capture_info', {}).get('timestamp', ''),
                    'total_strokes': 1,
                    'total_lines': len(stroke.get('lines', [])),
                    'total_bitmaps': 0
                },
                'strokes': [stroke]
            }

            # 创建临时渲染器用于缩略图
            thumb_renderer = DrawingRenderer(thumb_width, thumb_height)
            thumb_image = thumb_renderer.render_drawing_data(single_stroke_data)

            # 粘贴到网格中
            grid_image.paste(thumb_image, (x, y))

            # 添加stroke信息
            stroke_id = stroke.get('stroke_id', i + 1)
            lines_count = len(stroke.get('lines', []))
            info_text = f"Stroke {stroke_id}\n{lines_count} lines"

            try:
                info_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 12)
            except:
                info_font = ImageFont.load_default()

            grid_draw.text((x, y + thumb_height + 5), info_text, fill=(0, 0, 0), font=info_font)

        # 保存网格图像
        if output_file is None:
            base_name = os.path.splitext(input_file)[0]
            output_file = f"{base_name}_stroke_grid.png"

        grid_image.save(output_file, 'PNG')
        print(f"Stroke analysis grid saved to: {output_file}")

        return output_file
    
    def render_drawing_data(self, data: Dict[str, Any]) -> Image.Image:
        """渲染绘制数据为PIL图像"""
        # 创建画布
        image = Image.new('RGBA', (self.canvas_width, self.canvas_height), self.background_color)
        draw = ImageDraw.Draw(image)
        
        strokes = data.get('strokes', [])
        capture_info = data.get('capture_info', {})
        
        print(f"Rendering {len(strokes)} drawing elements...")
        print(f"Canvas size: {self.canvas_width}x{self.canvas_height}")
        
        # 按时间戳排序以确保正确的绘制顺序
        strokes.sort(key=lambda x: x.get('start_timestamp', x.get('timestamp', 0)))
        
        for i, stroke in enumerate(strokes):
            stroke_type = stroke.get('type', 'stroke')
            
            if stroke_type == 'bitmap':
                self._render_bitmap(draw, stroke)
            elif stroke_type == 'stroke':
                self._render_stroke(draw, stroke, i)
        
        print(f"Rendering completed. Total elements: {len(strokes)}")
        return image
    
    def _render_stroke(self, draw: ImageDraw.Draw, stroke: Dict[str, Any], stroke_index: int):
        """渲染单个笔画"""
        lines = stroke.get('lines', [])
        if not lines:
            return
        
        stroke_id = stroke.get('stroke_id', stroke_index)
        print(f"Rendering stroke {stroke_id} with {len(lines)} lines")
        
        # 按point_id排序确保正确的绘制顺序
        lines.sort(key=lambda x: x.get('point_id', 0))
        
        for line in lines:
            self._render_line(draw, line)
    
    def _render_line(self, draw: ImageDraw.Draw, line: Dict[str, Any]):
        """渲染单条线段"""
        start_x = line.get('start_x', 0)
        start_y = line.get('start_y', 0)
        stop_x = line.get('stop_x', 0)
        stop_y = line.get('stop_y', 0)
        stroke_width = line.get('stroke_width', 1.0)
        color = line.get('color', -16777216)  # 默认黑色
        alpha = line.get('alpha', 255)
        
        # 转换Android颜色值为RGB
        rgb_color = self._android_color_to_rgb(color, alpha)
        
        # 确保坐标在画布范围内
        start_x = max(0, min(self.canvas_width - 1, start_x))
        start_y = max(0, min(self.canvas_height - 1, start_y))
        stop_x = max(0, min(self.canvas_width - 1, stop_x))
        stop_y = max(0, min(self.canvas_height - 1, stop_y))
        
        # 绘制线段
        try:
            # PIL的line方法使用width参数设置线宽
            width = max(1, int(stroke_width))
            draw.line([(start_x, start_y), (stop_x, stop_y)], 
                     fill=rgb_color, width=width)
        except Exception as e:
            print(f"Error drawing line: {e}")
    
    def _render_bitmap(self, draw: ImageDraw.Draw, bitmap_info: Dict[str, Any]):
        """渲染位图（背景等）"""
        left = bitmap_info.get('left', 0)
        top = bitmap_info.get('top', 0)
        width = bitmap_info.get('width', 100)
        height = bitmap_info.get('height', 100)
        
        print(f"Rendering bitmap at ({left}, {top}) size {width}x{height}")
        
        # 绘制一个矩形表示位图区域
        draw.rectangle(
            [(left, top), (left + width, top + height)],
            outline=(200, 200, 200, 128),
            width=1
        )
    
    def _android_color_to_rgb(self, android_color: int, alpha: int = 255) -> Tuple[int, int, int, int]:
        """将Android颜色值转换为RGBA元组"""
        # Android颜色格式: ARGB (32位)
        # 如果是负数，转换为无符号32位整数
        if android_color < 0:
            android_color = android_color & 0xFFFFFFFF
        
        # 提取ARGB分量
        a = (android_color >> 24) & 0xFF
        r = (android_color >> 16) & 0xFF
        g = (android_color >> 8) & 0xFF
        b = android_color & 0xFF
        
        # 使用传入的alpha值（如果提供）
        if alpha != 255:
            a = alpha
        
        return (r, g, b, a)
    
    def create_analysis_image(self, data: Dict[str, Any], output_file: str = None) -> str:
        """创建绘制数据分析图像"""
        if output_file is None:
            output_file = "drawing_analysis.png"
        
        # 创建更大的画布用于分析
        analysis_width = self.canvas_width + 400
        analysis_height = self.canvas_height + 200
        
        image = Image.new('RGB', (analysis_width, analysis_height), (240, 240, 240))
        draw = ImageDraw.Draw(image)
        
        # 绘制主要内容区域
        main_area = (50, 50, 50 + self.canvas_width, 50 + self.canvas_height)
        draw.rectangle(main_area, fill=(255, 255, 255), outline=(0, 0, 0), width=2)
        
        # 在主区域内绘制内容
        main_image = self.render_drawing_data(data)
        image.paste(main_image, (50, 50))
        
        # 添加分析信息
        self._add_analysis_info(draw, data, analysis_width, analysis_height)
        
        image.save(output_file, 'PNG')
        print(f"Analysis image saved to: {output_file}")
        
        return output_file
    
    def _add_analysis_info(self, draw: ImageDraw.Draw, data: Dict[str, Any], 
                          width: int, height: int):
        """添加分析信息到图像"""
        capture_info = data.get('capture_info', {})
        strokes = data.get('strokes', [])
        
        # 信息文本区域
        info_x = self.canvas_width + 70
        info_y = 50
        line_height = 25
        
        # 绘制标题
        title = "Drawing Analysis"
        draw.text((info_x, info_y), title, fill=(0, 0, 0))
        info_y += line_height * 2
        
        # 基本统计信息
        stats = [
            f"Total Strokes: {capture_info.get('total_strokes', 0)}",
            f"Total Lines: {capture_info.get('total_lines', 0)}",
            f"Total Bitmaps: {capture_info.get('total_bitmaps', 0)}",
            f"Canvas Size: {self.canvas_width}x{self.canvas_height}",
        ]
        
        for stat in stats:
            draw.text((info_x, info_y), stat, fill=(0, 0, 0))
            info_y += line_height
        
        # 笔画详细信息
        info_y += line_height
        draw.text((info_x, info_y), "Stroke Details:", fill=(0, 0, 0))
        info_y += line_height
        
        stroke_count = 0
        for stroke in strokes:
            if stroke.get('type') == 'stroke' and stroke_count < 10:  # 只显示前10个笔画
                lines = stroke.get('lines', [])
                stroke_id = stroke.get('stroke_id', stroke_count)
                
                if lines:
                    first_line = lines[0]
                    stroke_width = first_line.get('stroke_width', 1.0)
                    color = first_line.get('color', 0)
                    
                    info_text = f"Stroke {stroke_id}: {len(lines)} lines, width={stroke_width:.1f}"
                    draw.text((info_x, info_y), info_text, fill=(0, 0, 0))
                    info_y += line_height
                    
                    stroke_count += 1
        
        if len([s for s in strokes if s.get('type') == 'stroke']) > 10:
            draw.text((info_x, info_y), "... (more strokes)", fill=(128, 128, 128))

def main():
    parser = argparse.ArgumentParser(description='Render captured drawing data to bitmap')
    parser.add_argument('input_file', help='Input JSON file with drawing data')
    parser.add_argument('-o', '--output', help='Output PNG file name')
    parser.add_argument('-w', '--width', type=int, default=1600, help='Canvas width (default: 1600)')
    parser.add_argument('--height', type=int, default=2560, help='Canvas height (default: 2560)')
    parser.add_argument('--analysis', action='store_true', help='Create analysis image with statistics')
    parser.add_argument('--individual', action='store_true', help='Render each stroke individually')
    parser.add_argument('--grid', action='store_true', help='Create stroke analysis grid')
    parser.add_argument('--output-dir', help='Output directory for individual strokes')
    
    args = parser.parse_args()
    
    renderer = DrawingRenderer(args.width, args.height)

    try:
        if args.individual:
            # 渲染每个stroke单独的图像
            output_files = renderer.render_individual_strokes(args.input_file, args.output_dir)
            print(f"Rendered {len(output_files)} individual stroke images")

        elif args.grid:
            # 创建stroke网格分析图
            output_file = args.output or "stroke_grid.png"
            renderer.create_stroke_analysis_grid(args.input_file, output_file)

        elif args.analysis:
            # 创建分析图像
            output_file = args.output or "drawing_analysis.png"
            renderer.create_analysis_image(
                json.load(open(args.input_file, 'r')),
                output_file
            )
        else:
            # 标准渲染
            renderer.render_from_file(args.input_file, args.output)

    except Exception as e:
        print(f"Error: {e}")
        return 1

    return 0

if __name__ == '__main__':
    exit(main())
