package com.onyx.demo.pen

import androidx.databinding.ObservableField
import androidx.databinding.ObservableInt

/**
 * 笔的状态数据类，包含所有可观察的属性
 * 这是MVVM架构中的Model层，用于存储和管理状态
 */
data class PenState(
    val motionEvent: ObservableField<String> = ObservableField(""),
    val loggingButtonText: ObservableField<String> = ObservableField(""),
    val penWidth: ObservableInt = ObservableInt(10),
    val pressureSensitivity: ObservableInt = ObservableInt(50),
    val velocitySensitivity: ObservableInt = ObservableInt(30),
    val brushSpacing: ObservableInt = ObservableInt(25)
) {
    
    /**
     * 笔类型枚举
     */
    enum class PenType {
        Ballpoint, Square, Fountain, XiuLi, Marker, BottomMaker, Pencil, PencilNew
    }

    /**
     * 当前选中的笔类型
     */
    var currentPenType: PenType = PenType.Fountain
        private set

    /**
     * 是否正在记录日志
     */
    var isLogging: Boolean = false
        private set

    /**
     * 更新笔类型
     */
    fun updatePenType(penType: PenType) {
        currentPenType = penType
    }
    
    /**
     * 更新日志状态
     */
    fun updateLoggingState(logging: Boolean, buttonText: String) {
        isLogging = logging
        loggingButtonText.set(buttonText)
    }
    
    /**
     * 重置所有状态到默认值
     */
    fun reset() {
        motionEvent.set("")
        loggingButtonText.set("")
        penWidth.set(10)
        pressureSensitivity.set(50)
        velocitySensitivity.set(30)
        brushSpacing.set(25)
        currentPenType = PenType.Fountain
        isLogging = false
    }
}
