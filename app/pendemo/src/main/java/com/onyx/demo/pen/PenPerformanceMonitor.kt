package com.onyx.demo.pen

import android.os.Handler
import android.os.Looper
import android.util.Log
import java.util.concurrent.atomic.AtomicLong
import java.util.concurrent.atomic.AtomicInteger

/**
 * 笔画绘制性能监控器
 * 用于监控多线程绘制的性能指标
 */
class PenPerformanceMonitor {
    
    companion object {
        private const val TAG = "PenPerformanceMonitor"
        private const val REPORT_INTERVAL_MS = 5000L // 5秒报告一次
    }
    
    // 性能计数器
    private val touchEventCount = AtomicLong(0)
    private val renderEventCount = AtomicLong(0)
    private val frameCount = AtomicLong(0)
    private val totalRenderTime = AtomicLong(0)
    private val maxRenderTime = AtomicLong(0)
    private val queueOverflowCount = AtomicLong(0)
    
    // 当前状态
    private val currentQueueSize = AtomicInteger(0)
    private val isProcessing = AtomicInteger(0)
    
    private val mainHandler = Handler(Looper.getMainLooper())
    private var isMonitoring = false
    
    /**
     * 开始性能监控
     */
    fun startMonitoring() {
        if (isMonitoring) return
        
        isMonitoring = true
        resetCounters()
        scheduleReport()
        
        Log.i(TAG, "Performance monitoring started")
    }
    
    /**
     * 停止性能监控
     */
    fun stopMonitoring() {
        isMonitoring = false
        mainHandler.removeCallbacksAndMessages(null)
        
        Log.i(TAG, "Performance monitoring stopped")
    }
    
    /**
     * 记录触摸事件
     */
    fun recordTouchEvent() {
        touchEventCount.incrementAndGet()
    }
    
    /**
     * 记录渲染事件开始
     */
    fun recordRenderStart(): Long {
        renderEventCount.incrementAndGet()
        return System.nanoTime()
    }
    
    /**
     * 记录渲染事件结束
     */
    fun recordRenderEnd(startTime: Long) {
        val renderTime = (System.nanoTime() - startTime) / 1_000_000 // 转换为毫秒
        totalRenderTime.addAndGet(renderTime)
        
        // 更新最大渲染时间
        var currentMax = maxRenderTime.get()
        while (renderTime > currentMax) {
            if (maxRenderTime.compareAndSet(currentMax, renderTime)) {
                break
            }
            currentMax = maxRenderTime.get()
        }
    }
    
    /**
     * 记录帧绘制
     */
    fun recordFrame() {
        frameCount.incrementAndGet()
    }
    
    /**
     * 记录队列溢出
     */
    fun recordQueueOverflow() {
        queueOverflowCount.incrementAndGet()
    }
    
    /**
     * 更新当前队列大小
     */
    fun updateQueueSize(size: Int) {
        currentQueueSize.set(size)
    }
    
    /**
     * 更新处理状态
     */
    fun updateProcessingState(processing: Boolean) {
        isProcessing.set(if (processing) 1 else 0)
    }
    
    /**
     * 获取性能统计信息
     */
    fun getPerformanceStats(): PerformanceStats {
        val renderCount = renderEventCount.get()
        val avgRenderTime = if (renderCount > 0) {
            totalRenderTime.get().toDouble() / renderCount
        } else 0.0
        
        return PerformanceStats(
            touchEvents = touchEventCount.get(),
            renderEvents = renderCount,
            frames = frameCount.get(),
            averageRenderTime = avgRenderTime,
            maxRenderTime = maxRenderTime.get(),
            queueOverflows = queueOverflowCount.get(),
            currentQueueSize = currentQueueSize.get(),
            isProcessing = isProcessing.get() == 1
        )
    }
    
    /**
     * 重置计数器
     */
    private fun resetCounters() {
        touchEventCount.set(0)
        renderEventCount.set(0)
        frameCount.set(0)
        totalRenderTime.set(0)
        maxRenderTime.set(0)
        queueOverflowCount.set(0)
        currentQueueSize.set(0)
        isProcessing.set(0)
    }
    
    /**
     * 定期报告性能数据
     */
    private fun scheduleReport() {
        if (!isMonitoring) return
        
        mainHandler.postDelayed({
            reportPerformance()
            scheduleReport()
        }, REPORT_INTERVAL_MS)
    }
    
    /**
     * 报告性能数据
     */
    private fun reportPerformance() {
        val stats = getPerformanceStats()
        
        Log.i(TAG, """
            Performance Report:
            - Touch Events: ${stats.touchEvents}
            - Render Events: ${stats.renderEvents}
            - Frames: ${stats.frames}
            - Avg Render Time: ${"%.2f".format(stats.averageRenderTime)}ms
            - Max Render Time: ${stats.maxRenderTime}ms
            - Queue Overflows: ${stats.queueOverflows}
            - Current Queue Size: ${stats.currentQueueSize}
            - Is Processing: ${stats.isProcessing}
            - FPS: ${"%.1f".format(stats.frames * 1000.0 / REPORT_INTERVAL_MS)}
        """.trimIndent())
    }
    
    /**
     * 性能统计数据类
     */
    data class PerformanceStats(
        val touchEvents: Long,
        val renderEvents: Long,
        val frames: Long,
        val averageRenderTime: Double,
        val maxRenderTime: Long,
        val queueOverflows: Long,
        val currentQueueSize: Int,
        val isProcessing: Boolean
    )
}
