package com.onyx.demo.pen

import android.graphics.Bitmap
import android.graphics.Paint
import com.onyx.android.sdk.pen.NeoPen
import java.io.InputStream

/**
 * PenView的契约接口，定义View层应该提供的功能
 * 这样ViewModel可以通过接口与View交互，而不直接依赖具体的View实现
 */
interface PenViewContract {
    
    /**
     * 设置笔和画笔
     */
    fun setPen(pen: NeoPen, paint: Paint)
    
    /**
     * 清除画布
     */
    fun clear()
    
    /**
     * 重绘画布
     */
    fun repaint()
    
    /**
     * 设置背景图片
     */
    fun setPenBackground(bitmap: Bitmap)
    
    /**
     * 日志相关功能
     */
    fun isLogging(): Boolean
    fun startLogging()
    fun stopLogging()
    fun saveLoggerData()
    fun loadLoggerDataAndRepaint(stream: InputStream)
    
    /**
     * 性能监控功能
     */
    fun startPerformanceMonitoring()
    fun stopPerformanceMonitoring()
    fun getPerformanceStats(): String
    fun getDetailedPerformanceStats(): PenPerformanceMonitor.PerformanceStats
}
