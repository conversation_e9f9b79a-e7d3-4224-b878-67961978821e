package com.onyx.demo.pen

import android.content.Context
import android.graphics.PointF
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.MotionEvent
import com.onyx.android.sdk.base.data.TouchPoint
import kotlin.math.cos
import kotlin.math.sin
import kotlin.random.Random

/**
 * PenView性能测试工具
 * 用于测试多线程优化后的性能表现
 */
class PenPerformanceTest(private val penView: PenView) {
    
    companion object {
        private const val TAG = "PenPerformanceTest"
    }
    
    private val mainHandler = Handler(Looper.getMainLooper())
    private var isRunning = false
    
    /**
     * 运行基本性能测试
     */
    fun runBasicPerformanceTest() {
        Log.i(TAG, "Starting basic performance test...")
        
        penView.startPerformanceMonitoring()
        
        // 模拟简单的直线绘制
        simulateLineDraw(
            start = PointF(100f, 100f),
            end = PointF(500f, 500f),
            duration = 2000L,
            pointCount = 100
        ) {
            Log.i(TAG, "Basic test completed")
            logPerformanceResults("Basic Test")
        }
    }
    
    /**
     * 运行压力测试
     */
    fun runStressTest() {
        Log.i(TAG, "Starting stress test...")
        
        penView.startPerformanceMonitoring()
        
        // 模拟复杂的螺旋绘制
        simulateSpiralDraw(
            center = PointF(400f, 400f),
            radius = 200f,
            duration = 5000L,
            pointCount = 500
        ) {
            Log.i(TAG, "Stress test completed")
            logPerformanceResults("Stress Test")
        }
    }
    
    /**
     * 运行多笔画测试
     */
    fun runMultiStrokeTest() {
        Log.i(TAG, "Starting multi-stroke test...")
        
        penView.startPerformanceMonitoring()
        
        // 模拟多个连续笔画
        simulateMultipleStrokes(
            strokeCount = 10,
            pointsPerStroke = 50,
            delay = 100L
        ) {
            Log.i(TAG, "Multi-stroke test completed")
            logPerformanceResults("Multi-Stroke Test")
        }
    }
    
    /**
     * 模拟直线绘制
     */
    private fun simulateLineDraw(
        start: PointF,
        end: PointF,
        duration: Long,
        pointCount: Int,
        onComplete: () -> Unit
    ) {
        val points = generateLinePoints(start, end, pointCount)
        simulateDrawing(points, duration, onComplete)
    }
    
    /**
     * 模拟螺旋绘制
     */
    private fun simulateSpiralDraw(
        center: PointF,
        radius: Float,
        duration: Long,
        pointCount: Int,
        onComplete: () -> Unit
    ) {
        val points = generateSpiralPoints(center, radius, pointCount)
        simulateDrawing(points, duration, onComplete)
    }
    
    /**
     * 模拟多笔画绘制
     */
    private fun simulateMultipleStrokes(
        strokeCount: Int,
        pointsPerStroke: Int,
        delay: Long,
        onComplete: () -> Unit
    ) {
        var currentStroke = 0
        
        fun drawNextStroke() {
            if (currentStroke >= strokeCount) {
                onComplete()
                return
            }
            
            val start = PointF(
                Random.nextFloat() * 600f + 100f,
                Random.nextFloat() * 600f + 100f
            )
            val end = PointF(
                Random.nextFloat() * 600f + 100f,
                Random.nextFloat() * 600f + 100f
            )
            
            val points = generateLinePoints(start, end, pointsPerStroke)
            simulateDrawing(points, 1000L) {
                currentStroke++
                mainHandler.postDelayed({ drawNextStroke() }, delay)
            }
        }
        
        drawNextStroke()
    }
    
    /**
     * 生成直线点序列
     */
    private fun generateLinePoints(start: PointF, end: PointF, count: Int): List<PointF> {
        val points = mutableListOf<PointF>()
        
        for (i in 0 until count) {
            val t = i.toFloat() / (count - 1)
            val x = start.x + (end.x - start.x) * t
            val y = start.y + (end.y - start.y) * t
            points.add(PointF(x, y))
        }
        
        return points
    }
    
    /**
     * 生成螺旋点序列
     */
    private fun generateSpiralPoints(center: PointF, radius: Float, count: Int): List<PointF> {
        val points = mutableListOf<PointF>()
        
        for (i in 0 until count) {
            val t = i.toFloat() / count
            val angle = t * 6 * Math.PI // 3圈螺旋
            val r = radius * t
            
            val x = center.x + (r * cos(angle)).toFloat()
            val y = center.y + (r * sin(angle)).toFloat()
            points.add(PointF(x, y))
        }
        
        return points
    }
    
    /**
     * 模拟绘制过程
     */
    private fun simulateDrawing(points: List<PointF>, duration: Long, onComplete: () -> Unit) {
        if (points.isEmpty()) {
            onComplete()
            return
        }
        
        isRunning = true
        val interval = duration / points.size
        var currentIndex = 0
        
        fun sendNextPoint() {
            if (!isRunning || currentIndex >= points.size) {
                if (currentIndex > 0) {
                    // 发送 ACTION_UP
                    sendTouchEvent(points[currentIndex - 1], MotionEvent.ACTION_UP)
                }
                isRunning = false
                onComplete()
                return
            }
            
            val point = points[currentIndex]
            val action = when (currentIndex) {
                0 -> MotionEvent.ACTION_DOWN
                points.size - 1 -> MotionEvent.ACTION_UP
                else -> MotionEvent.ACTION_MOVE
            }
            
            sendTouchEvent(point, action)
            currentIndex++
            
            if (currentIndex < points.size) {
                mainHandler.postDelayed({ sendNextPoint() }, interval)
            } else {
                sendNextPoint() // 发送最后的 ACTION_UP
            }
        }
        
        sendNextPoint()
    }
    
    /**
     * 发送触摸事件
     */
    private fun sendTouchEvent(point: PointF, action: Int) {
        val eventTime = System.currentTimeMillis()
        val motionEvent = MotionEvent.obtain(
            eventTime,
            eventTime,
            action,
            point.x,
            point.y,
            0
        )
        
        mainHandler.post {
            penView.onTouchEvent(motionEvent)
            motionEvent.recycle()
        }
    }
    
    /**
     * 记录性能结果
     */
    private fun logPerformanceResults(testName: String) {
        mainHandler.postDelayed({
            val stats = penView.getDetailedPerformanceStats()
            
            Log.i(TAG, """
                $testName Results:
                ==================
                Touch Events: ${stats.touchEvents}
                Render Events: ${stats.renderEvents}
                Frames: ${stats.frames}
                Average Render Time: ${"%.2f".format(stats.averageRenderTime)}ms
                Max Render Time: ${stats.maxRenderTime}ms
                Queue Overflows: ${stats.queueOverflows}
                Current Queue Size: ${stats.currentQueueSize}
                Is Processing: ${stats.isProcessing}
                
                Performance Summary:
                - Render Efficiency: ${"%.1f".format(stats.renderEvents.toDouble() / stats.touchEvents * 100)}%
                - Average FPS: ${"%.1f".format(stats.frames * 1000.0 / 5000)}
                - Render Latency: ${"%.2f".format(stats.averageRenderTime)}ms
                ==================
            """.trimIndent())
            
            penView.stopPerformanceMonitoring()
        }, 1000L) // 等待1秒让所有事件处理完成
    }
    
    /**
     * 停止当前测试
     */
    fun stopTest() {
        isRunning = false
        mainHandler.removeCallbacksAndMessages(null)
        penView.stopPerformanceMonitoring()
        Log.i(TAG, "Performance test stopped")
    }
}
