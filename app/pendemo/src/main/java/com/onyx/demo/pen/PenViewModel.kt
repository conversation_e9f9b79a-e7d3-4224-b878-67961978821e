package com.onyx.demo.pen

import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import androidx.databinding.Observable
import androidx.databinding.Observable.OnPropertyChangedCallback
import androidx.databinding.ObservableField
import androidx.databinding.ObservableInt
import com.onyx.android.sdk.pen.NeoBallpointInkPen
import com.onyx.android.sdk.pen.NeoBallpointPen
import com.onyx.android.sdk.pen.NeoCharcoalPen
import com.onyx.android.sdk.pen.NeoFountainPen
import com.onyx.android.sdk.pen.NeoMarkerPen
import com.onyx.android.sdk.pen.NeoPenConfig
import com.onyx.android.sdk.pen.NeoPencilPen
import com.onyx.android.sdk.pen.NeoSquarePen
import com.onyx.android.sdk.pen.NeoXiuLiPen

// TODO(joy): remove dependency of PenView
class PenViewModel(var penView: PenView, var penConfig: NeoPenConfig) {
    val valueMultiplier = 10
    val pressureSensitivityMultiplier = 100

    val motionEvent = ObservableField("")

    val loggingButtonText = ObservableField(penView.context.getString(R.string.start_logging))

    val penWidth = ObservableInt(penConfig.width.toInt())

    val pressureSensitivity = ObservableInt((valueMultiplier * penConfig.pressureSensitivity).toInt())
    val velocitySensitivity = ObservableInt((valueMultiplier * penConfig.velocitySensitivity).toInt())
    val brushSpacing = ObservableInt((valueMultiplier * penConfig.brushSpacing).toInt())

    companion object {
        enum class PenType {
            Ballpoint, Square, Fountain, XiuLi, Marker, BottomMaker, Pencil, PencilNew
        }
    }

    private var currentPen = PenType.Fountain

    init {
        penWidth.addOnPropertyChangedCallback(object : OnPropertyChangedCallback() {
            override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                penConfig.width = penWidth.get().toFloat()
                usePenWithConfig(currentPen, penConfig)
            }
        })

        pressureSensitivity.addOnPropertyChangedCallback(object : OnPropertyChangedCallback() {
            override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                penConfig.pressureSensitivity = pressureSensitivity.get().toFloat() / pressureSensitivityMultiplier
                usePenWithConfig(currentPen, penConfig)
            }
        })
        velocitySensitivity.addOnPropertyChangedCallback(object : OnPropertyChangedCallback() {
            override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                penConfig.velocitySensitivity = velocitySensitivity.get().toFloat() / valueMultiplier
                usePenWithConfig(currentPen, penConfig)
            }
        })
        brushSpacing.addOnPropertyChangedCallback(object : OnPropertyChangedCallback() {
            override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                penConfig.brushSpacing = brushSpacing.get().toFloat() / valueMultiplier
                usePenWithConfig(currentPen, penConfig)
            }
        })
    }

    private fun updatePenConfig(config: NeoPenConfig) {
        penConfig = config

        penWidth.set(penConfig.width.toInt())
        pressureSensitivity.set((pressureSensitivityMultiplier * penConfig.pressureSensitivity).toInt())
        velocitySensitivity.set((valueMultiplier * penConfig.velocitySensitivity).toInt())
        brushSpacing.set((valueMultiplier * penConfig.brushSpacing).toInt())
    }

    private fun usePenWithConfig(penType: PenType, config: NeoPenConfig) {
        when (penType) {
            PenType.Ballpoint -> penView.setPen(NeoBallpointInkPen.create(penConfig)!!, Paint().also {
                it.isAntiAlias = true
                it.isDither = true
                it.style = Paint.Style.STROKE
                it.strokeWidth = penConfig.width
            })
            PenType.Square -> penView.setPen(NeoSquarePen.create(penConfig)!!, Paint().also {
                it.isAntiAlias = true
                it.isDither = true
                it.style = Paint.Style.FILL
            })
            PenType.Fountain -> penView.setPen(NeoFountainPen.create(penConfig)!!, Paint().also {
                it.isAntiAlias = true
                it.isDither = true
                it.style = Paint.Style.STROKE
            })
            PenType.XiuLi -> penView.setPen(NeoXiuLiPen.create(penConfig)!!, Paint().also {
                it.isAntiAlias = true
                it.isDither = true
                it.strokeJoin = Paint.Join.ROUND
                it.strokeCap = Paint.Cap.ROUND
                it.style = Paint.Style.STROKE
            })
            PenType.Marker -> penView.setPen(NeoMarkerPen(), Paint().also {
                it.isAntiAlias = true
                it.isDither = true
                it.color = Color.MAGENTA
                it.alpha = 0x80
                it.style = Paint.Style.STROKE
                it.strokeWidth = 20f
                it.strokeCap = Paint.Cap.ROUND
                it.strokeJoin = Paint.Join.ROUND
            })
            PenType.BottomMaker -> penView.setPen(NeoMarkerPen(), Paint().also {
                it.isAntiAlias = true
                it.isDither = true
                it.color = Color.MAGENTA
                it.alpha = 0x80
                it.style = Paint.Style.STROKE
                it.strokeWidth = 20f
//            it.strokeCap = Paint.Cap.ROUND
                it.strokeJoin = Paint.Join.ROUND
                it.xfermode = PorterDuffXfermode(PorterDuff.Mode.DARKEN)
            })
            PenType.Pencil -> penView.setPen(NeoCharcoalPen.create(penConfig)!!, Paint().also {
                it.isAntiAlias = true
                it.isDither = true
            })
            PenType.PencilNew -> penView.setPen(NeoPencilPen.create(penConfig)!!, Paint().also {
                it.isAntiAlias = false
                it.isDither = false
                it.color = penConfig.color
            })
        }
    }

    fun toggleLogging() {
        if (penView.isLogging()) {
            penView.stopLogging()
            loggingButtonText.set(penView.context.getString(R.string.start_logging))
        } else {
            penView.startLogging()
            loggingButtonText.set(penView.context.getString(R.string.stop_logging))
        }
    }

    fun saveLoggerData() {
        penView.saveLoggerData()
    }

    fun clear() {
        penView.clear()
    }

    fun repaint() {
        penView.repaint()
    }

    fun useBallpointPen() {
        currentPen = PenType.Ballpoint
        updatePenConfig(NeoBallpointInkPen.defaultPenConfig().also {
            it.width = 3.0f
            it.fastMode = true
        })
        usePenWithConfig(currentPen, penConfig)
    }

    fun useSquarePen() {
        currentPen = PenType.Square
        updatePenConfig(NeoSquarePen.defaultPenConfig().also {
            it.width = 10.0f
            it.directionEnabled = true
            it.brushShape = NeoPenConfig.NEOPEN_BRUSH_SHAPE_ELLIPSE
        })
        usePenWithConfig(currentPen, penConfig)
    }

    fun useFountainPen() {
        currentPen = PenType.Fountain
        updatePenConfig(NeoFountainPen.defaultPenConfig().also {
            it.width = 10.0f // use larger pen width for 13" mate pad
            it.fastMode = true
            it.maxTouchPressure = 1.0f
            it.pressureSensitivity = 0.5f
            it.velocitySensitivity = 0.3f
        })
        usePenWithConfig(currentPen, penConfig)
    }

    fun useXiuLiPen() {
        currentPen = PenType.XiuLi
        updatePenConfig(NeoXiuLiPen.defaultPenConfig().also {
            it.width = 20.0f // use larger pen width for 13" mate pad
            it.minWidth = 1.0f
        })
        usePenWithConfig(currentPen, penConfig)
    }

    fun useMarkerPen() {
        currentPen = PenType.Marker
        updatePenConfig(NeoPenConfig())
        usePenWithConfig(currentPen, penConfig)
    }

    fun useBottomMarkerPen() {
        currentPen = PenType.BottomMaker
        updatePenConfig(NeoPenConfig())
        usePenWithConfig(currentPen, penConfig)
    }

    fun usePencilPen() {
        currentPen = PenType.Pencil
        updatePenConfig(NeoCharcoalPen.defaultPenConfig().also {
            it.width = 5.0f
            it.maxTouchPressure = 1.0f
        })
        usePenWithConfig(currentPen, penConfig)
    }

    fun usePencilPenNew() {
        currentPen = PenType.PencilNew
        updatePenConfig(NeoPencilPen.defaultPenConfig().also {
            it.color = Color.RED
            it.width = 10.0f
            it.maxTouchPressure = 1.0f
            it.pressureSensitivity = 1.0f
            it.tiltEnabled = false
            it.tiltScale = 2.0f
            it.brushSpacing = 0.25f
            it.brushShapes = mutableListOf(BitmapFactory.decodeResource(penView.resources, com.onyx.android.sdk.pen.R.drawable.pencil))
        })
        usePenWithConfig(currentPen, penConfig)
    }

}
