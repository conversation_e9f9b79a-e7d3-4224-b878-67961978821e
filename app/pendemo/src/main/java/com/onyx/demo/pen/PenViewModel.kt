package com.onyx.demo.pen

import android.content.Context
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import androidx.databinding.Observable
import androidx.databinding.Observable.OnPropertyChangedCallback
import androidx.lifecycle.ViewModel
import com.onyx.android.sdk.pen.NeoBallpointInkPen
import com.onyx.android.sdk.pen.NeoBallpointPen
import com.onyx.android.sdk.pen.NeoCharcoalPen
import com.onyx.android.sdk.pen.NeoFountainPen
import com.onyx.android.sdk.pen.NeoMarkerPen
import com.onyx.android.sdk.pen.NeoPenConfig
import com.onyx.android.sdk.pen.NeoPencilPen
import com.onyx.android.sdk.pen.NeoSquarePen
import com.onyx.android.sdk.pen.NeoXiuLiPen

// 类型别名，用于向后兼容
typealias PenType = PenState.PenType

/**
 * 重构后的PenViewModel，遵循MVVM架构原则
 * - 不直接依赖View层
 * - 通过接口与View交互
 * - 管理业务逻辑和状态
 */
class PenViewModel(
    private val context: Context,
    private var penConfig: NeoPenConfig
) : ViewModel() {
    // 常量
    private val valueMultiplier = 10
    private val pressureSensitivityMultiplier = 100

    // View接口引用，通过依赖注入设置
    private var penViewContract: PenViewContract? = null

    // 状态管理
    val penState = PenState().apply {
        loggingButtonText.set(context.getString(R.string.start_logging))
        penWidth.set(penConfig.width.toInt())
        pressureSensitivity.set((pressureSensitivityMultiplier * penConfig.pressureSensitivity).toInt())
        velocitySensitivity.set((valueMultiplier * penConfig.velocitySensitivity).toInt())
        brushSpacing.set((valueMultiplier * penConfig.brushSpacing).toInt())
    }

    // 为了保持数据绑定兼容性，暴露状态属性
    val motionEvent = penState.motionEvent
    val loggingButtonText = penState.loggingButtonText
    val penWidth = penState.penWidth
    val pressureSensitivity = penState.pressureSensitivity
    val velocitySensitivity = penState.velocitySensitivity
    val brushSpacing = penState.brushSpacing



    /**
     * 设置View接口，实现依赖注入
     */
    fun setPenViewContract(contract: PenViewContract) {
        penViewContract = contract
    }

    init {
        setupPropertyChangeListeners()
    }

    /**
     * 设置属性变化监听器
     */
    private fun setupPropertyChangeListeners() {
        penWidth.addOnPropertyChangedCallback(object : OnPropertyChangedCallback() {
            override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                penConfig.width = penWidth.get().toFloat()
                usePenWithConfig(penState.currentPenType, penConfig)
            }
        })

        pressureSensitivity.addOnPropertyChangedCallback(object : OnPropertyChangedCallback() {
            override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                penConfig.pressureSensitivity = pressureSensitivity.get().toFloat() / pressureSensitivityMultiplier
                usePenWithConfig(penState.currentPenType, penConfig)
            }
        })

        velocitySensitivity.addOnPropertyChangedCallback(object : OnPropertyChangedCallback() {
            override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                penConfig.velocitySensitivity = velocitySensitivity.get().toFloat() / valueMultiplier
                usePenWithConfig(penState.currentPenType, penConfig)
            }
        })

        brushSpacing.addOnPropertyChangedCallback(object : OnPropertyChangedCallback() {
            override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                penConfig.brushSpacing = brushSpacing.get().toFloat() / valueMultiplier
                usePenWithConfig(penState.currentPenType, penConfig)
            }
        })
    }

    private fun updatePenConfig(config: NeoPenConfig) {
        penConfig = config

        penWidth.set(penConfig.width.toInt())
        pressureSensitivity.set((pressureSensitivityMultiplier * penConfig.pressureSensitivity).toInt())
        velocitySensitivity.set((valueMultiplier * penConfig.velocitySensitivity).toInt())
        brushSpacing.set((valueMultiplier * penConfig.brushSpacing).toInt())
    }

    /**
     * 使用指定配置创建笔和画笔
     */
    private fun usePenWithConfig(penType: PenType, config: NeoPenConfig) {
        val (pen, paint) = createPenAndPaint(penType, config)
        penViewContract?.setPen(pen, paint)
    }

    /**
     * 根据笔类型和配置创建笔和画笔对象
     */
    private fun createPenAndPaint(penType: PenType, config: NeoPenConfig): Pair<com.onyx.android.sdk.pen.NeoPen, Paint> {
        return when (penType) {
            PenType.Ballpoint -> Pair(
                NeoBallpointInkPen.create(config)!!,
                Paint().apply {
                    isAntiAlias = true
                    isDither = true
                    style = Paint.Style.STROKE
                    strokeWidth = config.width
                }
            )
            PenType.Square -> Pair(
                NeoSquarePen.create(config)!!,
                Paint().apply {
                    isAntiAlias = true
                    isDither = true
                    style = Paint.Style.FILL
                }
            )
            PenType.Fountain -> Pair(
                NeoFountainPen.create(config)!!,
                Paint().apply {
                    isAntiAlias = true
                    isDither = true
                    style = Paint.Style.STROKE
                }
            )
            PenType.XiuLi -> Pair(
                NeoXiuLiPen.create(config)!!,
                Paint().apply {
                    isAntiAlias = true
                    isDither = true
                    strokeJoin = Paint.Join.ROUND
                    strokeCap = Paint.Cap.ROUND
                    style = Paint.Style.STROKE
                }
            )
            PenType.Marker -> Pair(
                NeoMarkerPen(),
                Paint().apply {
                    isAntiAlias = true
                    isDither = true
                    color = Color.MAGENTA
                    alpha = 0x80
                    style = Paint.Style.STROKE
                    strokeWidth = 20f
                    strokeCap = Paint.Cap.ROUND
                    strokeJoin = Paint.Join.ROUND
                }
            )
            PenType.BottomMaker -> Pair(
                NeoMarkerPen(),
                Paint().apply {
                    isAntiAlias = true
                    isDither = true
                    color = Color.MAGENTA
                    alpha = 0x80
                    style = Paint.Style.STROKE
                    strokeWidth = 20f
                    strokeJoin = Paint.Join.ROUND
                    xfermode = PorterDuffXfermode(PorterDuff.Mode.DARKEN)
                }
            )
            PenType.Pencil -> Pair(
                NeoCharcoalPen.create(config)!!,
                Paint().apply {
                    isAntiAlias = true
                    isDither = true
                }
            )
            PenType.PencilNew -> Pair(
                NeoPencilPen.create(config)!!,
                Paint().apply {
                    isAntiAlias = false
                    isDither = false
                    color = config.color
                }
            )
        }
    }

    /**
     * UI交互方法 - 通过接口与View交互
     */
    fun toggleLogging() {
        val contract = penViewContract ?: return

        if (contract.isLogging()) {
            contract.stopLogging()
            penState.updateLoggingState(false, context.getString(R.string.start_logging))
        } else {
            contract.startLogging()
            penState.updateLoggingState(true, context.getString(R.string.stop_logging))
        }
    }

    fun saveLoggerData() {
        penViewContract?.saveLoggerData()
    }

    fun clear() {
        penViewContract?.clear()
    }

    fun repaint() {
        penViewContract?.repaint()
    }

    /**
     * 笔类型切换方法
     */
    fun useBallpointPen() {
        penState.updatePenType(PenType.Ballpoint)
        updatePenConfig(NeoBallpointInkPen.defaultPenConfig().also {
            it.width = 3.0f
            it.fastMode = true
        })
        usePenWithConfig(penState.currentPenType, penConfig)
    }

    fun useSquarePen() {
        penState.updatePenType(PenType.Square)
        updatePenConfig(NeoSquarePen.defaultPenConfig().also {
            it.width = 10.0f
            it.directionEnabled = true
            it.brushShape = NeoPenConfig.NEOPEN_BRUSH_SHAPE_ELLIPSE
        })
        usePenWithConfig(penState.currentPenType, penConfig)
    }

    fun useFountainPen() {
        penState.updatePenType(PenType.Fountain)
        updatePenConfig(NeoFountainPen.defaultPenConfig().also {
            it.width = 10.0f // use larger pen width for 13" mate pad
            it.fastMode = true
            it.maxTouchPressure = 1.0f
            it.pressureSensitivity = 0.5f
            it.velocitySensitivity = 0.3f
        })
        usePenWithConfig(penState.currentPenType, penConfig)
    }

    fun useXiuLiPen() {
        penState.updatePenType(PenType.XiuLi)
        updatePenConfig(NeoXiuLiPen.defaultPenConfig().also {
            it.width = 20.0f // use larger pen width for 13" mate pad
            it.minWidth = 1.0f
        })
        usePenWithConfig(penState.currentPenType, penConfig)
    }

    fun useMarkerPen() {
        penState.updatePenType(PenType.Marker)
        updatePenConfig(NeoPenConfig())
        usePenWithConfig(penState.currentPenType, penConfig)
    }

    fun useBottomMarkerPen() {
        penState.updatePenType(PenType.BottomMaker)
        updatePenConfig(NeoPenConfig())
        usePenWithConfig(penState.currentPenType, penConfig)
    }

    fun usePencilPen() {
        penState.updatePenType(PenType.Pencil)
        updatePenConfig(NeoCharcoalPen.defaultPenConfig().also {
            it.width = 5.0f
            it.maxTouchPressure = 1.0f
        })
        usePenWithConfig(penState.currentPenType, penConfig)
    }

    fun usePencilPenNew() {
        penState.updatePenType(PenType.PencilNew)
        updatePenConfig(NeoPencilPen.defaultPenConfig().also {
            it.color = Color.RED
            it.width = 10.0f
            it.maxTouchPressure = 1.0f
            it.pressureSensitivity = 1.0f
            it.tiltEnabled = false
            it.tiltScale = 2.0f
            it.brushSpacing = 0.25f
            it.brushShapes = mutableListOf(BitmapFactory.decodeResource(context.resources, com.onyx.android.sdk.pen.R.drawable.pencil))
        })
        usePenWithConfig(penState.currentPenType, penConfig)
    }

    /**
     * 获取当前笔类型
     */
    fun getCurrentPenType(): PenType = penState.currentPenType

    /**
     * 获取当前笔配置
     */
    fun getCurrentPenConfig(): NeoPenConfig = penConfig

}
