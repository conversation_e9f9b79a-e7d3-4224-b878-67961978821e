package com.onyx.demo.pen

import android.content.Context
import android.util.AttributeSet
import android.view.ViewGroup

class FlowLayout : ViewGroup {

    var margin = 0

    constructor(context: Context) : super(context, null)

    constructor(context: Context, attributeSet: AttributeSet?) : super(context, attributeSet) {
        init(context, attributeSet)
    }

    protected fun init(context: Context, attributeSet: AttributeSet?) {
        margin = toPx(2f)
    }

    override fun onMeasure(wSpec: Int, hSpec: Int) {
        val xMode = MeasureSpec.getMode(wSpec)
        val yMode = MeasureSpec.getMode(hSpec)
        val xSize = MeasureSpec.getSize(wSpec)
        var ySize = MeasureSpec.getSize(hSpec)

        if (xMode == MeasureSpec.AT_MOST) {
            throw RuntimeException("FlowLayout must exactly specify width")
        }
        var usedWidth = 0
        var remainWidth = 0
        var totalHeight = 0 // height of all lines
        var lineHeight = 0 // height of current view
        var maxLineHeight = 0 // final height of line

        // measure layout size by child param
        for (i in 0 until childCount) {
            remainWidth = xSize - usedWidth - paddingLeft - paddingRight
            val child = getChildAt(i)
            measureChild(child, wSpec, hSpec)

            // this line is full
            if (remainWidth < child.measuredWidth) {
                // finish current line
                totalHeight += maxLineHeight
                // begin next line
                maxLineHeight = 0
                usedWidth = 0
            }

            // sum child width and margin
            val lp = child.layoutParams as MarginLayoutParams
            usedWidth += lp.leftMargin + lp.rightMargin + child.measuredWidth
            // update height of current line
            lineHeight = child.measuredHeight + lp.topMargin + lp.bottomMargin
            maxLineHeight = Math.max(lineHeight, maxLineHeight)
        }

        // decide final height of layout
        totalHeight += maxLineHeight + paddingTop + paddingBottom
        if (yMode == MeasureSpec.AT_MOST) {
            ySize = Math.min(totalHeight, ySize)
        }
        super.setMeasuredDimension(xSize, ySize)
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        val width = right - left
        val paddingLeft = paddingLeft
        val paddingRight = paddingRight
        val paddingTop = paddingTop
        var childTop = paddingTop
        var childLeft = paddingLeft
        var usedWidth = 0
        val remainWidth = width - paddingLeft - paddingRight
        var totalHeight = paddingTop
        var lineHeight = 0
        var maxLineHeight = 0

        // decide position for all child
        for (i in 0 until childCount) {
            val child = getChildAt(i)
            val childWidth = child.measuredWidth
            val childHeight = child.measuredHeight
            val lp = child.layoutParams as MarginLayoutParams
            usedWidth += lp.leftMargin + lp.rightMargin + childWidth
            lineHeight = childHeight + lp.topMargin + lp.bottomMargin

            // current line if full
            if (remainWidth < usedWidth) {
                // decide start position for next line
                totalHeight += maxLineHeight
                childLeft = paddingLeft
                childTop = totalHeight
                // switch to next line
                maxLineHeight = 0
                usedWidth = lp.leftMargin + childWidth + lp.rightMargin
            }
            maxLineHeight = Math.max(lineHeight, maxLineHeight)

            // decide final position of current child
            child.layout(childLeft + lp.leftMargin, childTop + lp.topMargin, childLeft + lp.leftMargin + childWidth, childTop + lp.topMargin + childHeight)

            // decide position for next child
            childLeft += lp.leftMargin + childWidth + lp.rightMargin
        }
    }

    override fun generateLayoutParams(attrs: AttributeSet): LayoutParams {
        val lp = MarginLayoutParams(getContext(), attrs)
        lp.setMargins(margin, margin, margin, margin)
        return lp
    }

    override fun generateDefaultLayoutParams(): LayoutParams {
        val lp = MarginLayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
        lp.setMargins(margin, margin, margin, margin)
        return lp
    }

    override fun generateLayoutParams(p: LayoutParams): LayoutParams {
        val lp = MarginLayoutParams(p)
        lp.setMargins(margin, margin, margin, margin)
        return lp
    }

    fun toPx(dp: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (dp * scale + 0.5f).toInt()
    }

    fun toDp(px: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (px / scale + 0.5f).toInt()
    }
}