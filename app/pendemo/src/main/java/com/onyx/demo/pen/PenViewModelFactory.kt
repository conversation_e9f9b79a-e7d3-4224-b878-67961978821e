package com.onyx.demo.pen

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.onyx.android.sdk.pen.NeoPenConfig

/**
 * PenViewModel的工厂类，用于创建ViewModel实例
 * 遵循Android Architecture Components的最佳实践
 */
class PenViewModelFactory(
    private val context: Context,
    private val penConfig: NeoPenConfig
) : ViewModelProvider.Factory {

    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(PenViewModel::class.java)) {
            return PenViewModel(context, penConfig) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class: ${modelClass.name}")
    }
}
