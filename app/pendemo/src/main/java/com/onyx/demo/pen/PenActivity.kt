package com.onyx.demo.pen

import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.ParcelFileDescriptor
import android.view.View
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import com.onyx.android.sdk.base.utils.ResManager
import com.onyx.android.sdk.pen.NeoPenConfig
import com.onyx.demo.pen.databinding.ActivityPenBinding
import java.io.FileInputStream


@RequiresApi(Build.VERSION_CODES.Q)
class PenActivity : AppCompatActivity() {
    private val OPEN_BACKGROUND_IMAGE_REQUEST_CODE = 0x33
    private val OPEN_LOGGER_DATA_REQUEST_CODE = 0x34

    private lateinit var binding: ActivityPenBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        ResManager.init(applicationContext)

        binding = DataBindingUtil.setContentView(this, R.layout.activity_pen)
        binding.penModel = PenViewModel(binding.penView, NeoPenConfig())

        val display = windowManager.defaultDisplay
        val refreshRate = display.refreshRate
        (findViewById<View>(R.id.text_view_display_rate) as TextView).text = refreshRate.toString()

        findViewById<View>(R.id.button_background).setOnClickListener { openFilePicker(OPEN_BACKGROUND_IMAGE_REQUEST_CODE) }
        findViewById<View>(R.id.button_load_logger).setOnClickListener { openFilePicker(OPEN_LOGGER_DATA_REQUEST_CODE) }

        (binding.penModel as PenViewModel).useFountainPen()

        askForFilePermissions()
    }

    override fun onActivityResult(
        requestCode: Int, resultCode: Int, resultData: Intent?) {
        super.onActivityResult(requestCode, resultCode, resultData)

        if ((requestCode == OPEN_BACKGROUND_IMAGE_REQUEST_CODE
            || requestCode == OPEN_LOGGER_DATA_REQUEST_CODE)
            && resultCode == Activity.RESULT_OK) {
            // The result data contains a URI for the document or directory that
            // the user selected.
            resultData?.data?.also { uri ->
                contentResolver.takePersistableUriPermission(
                    uri,
                    Intent.FLAG_GRANT_READ_URI_PERMISSION
                )

                if (requestCode == OPEN_BACKGROUND_IMAGE_REQUEST_CODE) {
                    val bitmap = getBitmapFromUri(uri)
                    if (bitmap != null) {
                        binding.penView.setBackground(bitmap)
                    }
                } else {
                    val stream = getStreamFromUri(uri)
                    if (stream != null) {
                        binding.penView.loadLoggerDataAndRepaint(stream)
                    }
                }
            }
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        when (requestCode) {
            1 -> {

                // If request is cancelled, the result arrays are empty.
                if (grantResults.isNotEmpty() && grantResults[0] === PackageManager.PERMISSION_GRANTED) {
                    Toast.makeText(this, "Write permission granted!", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(this, "Write permission denied!", Toast.LENGTH_SHORT).show()
                }
            }
        }
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    private fun askForFilePermissions(): Boolean {
        if (Build.VERSION.SDK_INT >= 23) {
            val hasPermission =
                checkSelfPermission(android.Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
            if (!hasPermission) {
                requestPermissions(
                        arrayOf(
                            android.Manifest.permission.WRITE_EXTERNAL_STORAGE,
                            android.Manifest.permission.READ_EXTERNAL_STORAGE
                    ), 1
                )
                return true
            }
        }
        return false
    }

    private fun getBitmapFromUri(uri: Uri): Bitmap? {
        val parcelFileDescriptor: ParcelFileDescriptor? =
            contentResolver.openFileDescriptor(uri, "r")
        val fileDescriptor = parcelFileDescriptor?.fileDescriptor
        val image: Bitmap = BitmapFactory.decodeFileDescriptor(fileDescriptor)
        parcelFileDescriptor?.close()
        return image
    }

    private fun getStreamFromUri(uri: Uri): FileInputStream {
        val parcelFileDescriptor: ParcelFileDescriptor? =
            contentResolver.openFileDescriptor(uri, "r")
        return FileInputStream(parcelFileDescriptor?.fileDescriptor)
    }

    private fun openFilePicker(code: Int) {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            type = "*/*"

            /**
             * Because we'll want to use [ContentResolver.openFileDescriptor] to read
             * the data of whatever file is picked, we set [Intent.CATEGORY_OPENABLE]
             * to ensure this will succeed.
             */
            addCategory(Intent.CATEGORY_OPENABLE)
        }

        startActivityForResult(intent, code)
    }
}