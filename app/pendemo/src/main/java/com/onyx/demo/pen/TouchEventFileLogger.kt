package com.onyx.demo.pen

import android.content.Context
import android.view.MotionEvent
import com.onyx.android.sdk.base.data.TouchPoint
import org.json.JSONObject
import java.io.File
import java.io.FileWriter
import java.io.InputStream

class TouchEventFileLogger(private val file: File, private val writer: FileWriter) {
    companion object {
        fun createLogger(context: Context): TouchEventFileLogger {
            val file = File(context.filesDir,
                "touch_points_" + System.currentTimeMillis() + ".json")
            return TouchEventFileLogger(file, FileWriter(file))
        }

        fun loadTouchPoints(stream: InputStream): ArrayList<Pair<Int, TouchPoint>> {
            val lines = stream.bufferedReader().readLines()

            val points = ArrayList<Pair<Int, TouchPoint>>()
            lines.forEach {
                val obj = JSONObject(it)
                val action = obj.getInt("action");
                val x = obj.getDouble("x").toFloat()
                val y = obj.getDouble("y").toFloat()
                val pressure = obj.getDouble("pressure").toFloat()
                val size = obj.getDouble("size").toFloat()
                val tilt = obj.getDouble("tilt").toFloat()
                val orientation = obj.getDouble("orientation").toFloat()
                val ts = obj.getLong("timestamp")

                val (tx, ty) = TouchPoint.computeAndroidTiltXY(tilt, orientation)

                points.add(Pair(action, TouchPoint(x, y, pressure, size, tx, ty, ts)))
            }

            return points
        }
    }

    fun onEvent(event: MotionEvent) {
        val obj = JSONObject()
        obj.put("action", event.action)
        obj.put("x", event.x)
        obj.put("y", event.y)
        obj.put("pressure", event.pressure)
        obj.put("size", event.size)
        obj.put("tilt", event.getAxisValue(MotionEvent.AXIS_TILT))
        obj.put("orientation", event.getAxisValue(MotionEvent.AXIS_ORIENTATION))
        obj.put("timestamp", event.eventTime)
        writer.appendLine(obj.toString())

        if (event.action == MotionEvent.ACTION_UP) {
            writer.flush()
        }
    }

    fun close() {
        writer.close()
    }

    fun saveToExternalStorage(): File {
        return file.copyTo(File("/sdcard/Documents", file.name))
    }
}