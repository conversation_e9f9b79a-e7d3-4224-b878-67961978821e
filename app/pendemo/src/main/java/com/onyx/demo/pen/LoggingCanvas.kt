package com.onyx.demo.pen

import android.graphics.*
import android.util.Log

/**
 * Canvas包装器，用于记录所有绘制操作到logcat
 * 特别关注drawLine调用，记录坐标和画笔信息
 */
class LoggingCanvas(private val originalCanvas: Canvas) : Canvas() {

    companion object {
        private const val TAG = "PenDrawLog"
        private var strokeId = 0
        private var pointId = 0
    }

    // 当前笔画ID，用于关联同一笔画的多个点
    private var currentStrokeId = -1

    init {
        Log.d(TAG, "LoggingCanvas created with size: ${originalCanvas.width}x${originalCanvas.height}")
    }
    
    override fun drawLine(startX: Float, startY: Float, stopX: Float, stopY: Float, paint: Paint) {
        // 记录drawLine调用
        logDrawLine(startX, startY, stopX, stopY, paint)
        
        // 执行实际绘制
        originalCanvas.drawLine(startX, startY, stopX, stopY, paint)
    }
    
    override fun drawPath(path: Path, paint: Paint) {
        // 记录drawPath调用
        logDrawPath(path, paint)
        
        // 执行实际绘制
        originalCanvas.drawPath(path, paint)
    }
    
    override fun drawBitmap(bitmap: Bitmap, left: Float, top: Float, paint: Paint?) {
        // 记录位图绘制（背景等）
        Log.d(TAG, "DRAW_BITMAP|left=$left|top=$top|width=${bitmap.width}|height=${bitmap.height}")
        
        // 执行实际绘制
        originalCanvas.drawBitmap(bitmap, left, top, paint)
    }
    
    override fun drawBitmap(bitmap: Bitmap, src: Rect?, dst: Rect, paint: Paint?) {
        Log.d(TAG, "DRAW_BITMAP_RECT|dst=${dst.toShortString()}|width=${bitmap.width}|height=${bitmap.height}")
        originalCanvas.drawBitmap(bitmap, src, dst, paint)
    }
    
    override fun drawBitmap(bitmap: Bitmap, src: Rect?, dst: RectF, paint: Paint?) {
        Log.d(TAG, "DRAW_BITMAP_RECTF|dst=${dst.toShortString()}|width=${bitmap.width}|height=${bitmap.height}")
        originalCanvas.drawBitmap(bitmap, src, dst, paint)
    }
    
    /**
     * 记录drawLine调用的详细信息
     */
    private fun logDrawLine(startX: Float, startY: Float, stopX: Float, stopY: Float, paint: Paint) {
        val strokeWidth = paint.strokeWidth
        val color = paint.color
        val alpha = paint.alpha
        val timestamp = System.currentTimeMillis()

        // 检测是否是新笔画的开始（通过坐标变化判断）
        if (shouldStartNewStroke(startX, startY)) {
            currentStrokeId = ++strokeId
            pointId = 0
            Log.d(TAG, "STROKE_START|strokeId=$currentStrokeId|timestamp=$timestamp")
        }

        // 记录线段信息
        Log.d(TAG, "DRAW_LINE|strokeId=$currentStrokeId|pointId=${++pointId}|" +
                "startX=$startX|startY=$startY|stopX=$stopX|stopY=$stopY|" +
                "strokeWidth=$strokeWidth|color=$color|alpha=$alpha|timestamp=$timestamp")

        // 更新最后的终点坐标
        updateLastEndPoint(stopX, stopY)
    }
    
    /**
     * 记录drawPath调用的信息
     */
    private fun logDrawPath(path: Path, paint: Paint) {
        val strokeWidth = paint.strokeWidth
        val color = paint.color
        val alpha = paint.alpha
        val timestamp = System.currentTimeMillis()
        
        currentStrokeId = ++strokeId
        
        Log.d(TAG, "DRAW_PATH|strokeId=$currentStrokeId|" +
                "strokeWidth=$strokeWidth|color=$color|alpha=$alpha|timestamp=$timestamp")
    }
    
    /**
     * 判断是否应该开始新的笔画
     * 简单的启发式方法：如果起点距离上一个终点较远，认为是新笔画
     */
    private var lastEndX = Float.NaN
    private var lastEndY = Float.NaN
    
    private fun shouldStartNewStroke(startX: Float, startY: Float): Boolean {
        if (lastEndX.isNaN() || lastEndY.isNaN()) {
            return true
        }
        
        val distance = kotlin.math.sqrt(
            (startX - lastEndX) * (startX - lastEndX) + 
            (startY - lastEndY) * (startY - lastEndY)
        )
        
        return distance > 10f // 距离阈值，可调整
    }
    
    /**
     * 更新最后的终点坐标
     */
    private fun updateLastEndPoint(stopX: Float, stopY: Float) {
        lastEndX = stopX
        lastEndY = stopY
    }
    
    // 委托所有其他Canvas方法到原始Canvas
    override fun getWidth(): Int = originalCanvas.width
    override fun getHeight(): Int = originalCanvas.height
    override fun getDensity(): Int = originalCanvas.density
    override fun setDensity(density: Int) = originalCanvas.setDensity(density)
    override fun getMaximumBitmapWidth(): Int = originalCanvas.maximumBitmapWidth
    override fun getMaximumBitmapHeight(): Int = originalCanvas.maximumBitmapHeight
    
    override fun save(): Int = originalCanvas.save()
    override fun saveLayer(bounds: RectF?, paint: Paint?): Int = originalCanvas.saveLayer(bounds, paint)
    override fun saveLayer(left: Float, top: Float, right: Float, bottom: Float, paint: Paint?): Int = 
        originalCanvas.saveLayer(left, top, right, bottom, paint)
    override fun saveLayerAlpha(bounds: RectF?, alpha: Int): Int = originalCanvas.saveLayerAlpha(bounds, alpha)
    override fun saveLayerAlpha(left: Float, top: Float, right: Float, bottom: Float, alpha: Int): Int = 
        originalCanvas.saveLayerAlpha(left, top, right, bottom, alpha)
    override fun restore() = originalCanvas.restore()
    override fun getSaveCount(): Int = originalCanvas.saveCount
    override fun restoreToCount(saveCount: Int) = originalCanvas.restoreToCount(saveCount)
    
    override fun translate(dx: Float, dy: Float) = originalCanvas.translate(dx, dy)
    override fun scale(sx: Float, sy: Float) = originalCanvas.scale(sx, sy)
    override fun rotate(degrees: Float) = originalCanvas.rotate(degrees)
    override fun skew(sx: Float, sy: Float) = originalCanvas.skew(sx, sy)
    override fun concat(matrix: Matrix?) = originalCanvas.concat(matrix)
    override fun setMatrix(matrix: Matrix?) = originalCanvas.setMatrix(matrix)
    override fun getMatrix(ctm: Matrix) = originalCanvas.getMatrix(ctm)
    fun getMatrixCompat(): Matrix = originalCanvas.matrix
    
    override fun clipRect(rect: RectF): Boolean = originalCanvas.clipRect(rect)
    override fun clipRect(rect: Rect): Boolean = originalCanvas.clipRect(rect)
    override fun clipRect(left: Float, top: Float, right: Float, bottom: Float): Boolean = 
        originalCanvas.clipRect(left, top, right, bottom)
    override fun clipRect(left: Int, top: Int, right: Int, bottom: Int): Boolean = 
        originalCanvas.clipRect(left, top, right, bottom)
    override fun clipPath(path: Path): Boolean = originalCanvas.clipPath(path)
    override fun getClipBounds(bounds: Rect): Boolean = originalCanvas.getClipBounds(bounds)
    fun getClipBoundsCompat(): Rect = originalCanvas.clipBounds
    override fun quickReject(rect: RectF, type: EdgeType): Boolean = originalCanvas.quickReject(rect, type)
    override fun quickReject(path: Path, type: EdgeType): Boolean = originalCanvas.quickReject(path, type)
    override fun quickReject(left: Float, top: Float, right: Float, bottom: Float, type: EdgeType): Boolean = 
        originalCanvas.quickReject(left, top, right, bottom, type)
    
    override fun drawColor(color: Int) = originalCanvas.drawColor(color)
    override fun drawColor(color: Int, mode: PorterDuff.Mode) = originalCanvas.drawColor(color, mode)
    override fun drawPaint(paint: Paint) = originalCanvas.drawPaint(paint)
    override fun drawPoint(x: Float, y: Float, paint: Paint) = originalCanvas.drawPoint(x, y, paint)
    override fun drawPoints(pts: FloatArray?, offset: Int, count: Int, paint: Paint) = 
        originalCanvas.drawPoints(pts, offset, count, paint)
    override fun drawPoints(pts: FloatArray, paint: Paint) = originalCanvas.drawPoints(pts, paint)
    override fun drawRect(rect: RectF, paint: Paint) = originalCanvas.drawRect(rect, paint)
    override fun drawRect(r: Rect, paint: Paint) = originalCanvas.drawRect(r, paint)
    override fun drawRect(left: Float, top: Float, right: Float, bottom: Float, paint: Paint) = 
        originalCanvas.drawRect(left, top, right, bottom, paint)
    override fun drawOval(oval: RectF, paint: Paint) = originalCanvas.drawOval(oval, paint)
    override fun drawCircle(cx: Float, cy: Float, radius: Float, paint: Paint) = 
        originalCanvas.drawCircle(cx, cy, radius, paint)
    override fun drawArc(oval: RectF, startAngle: Float, sweepAngle: Float, useCenter: Boolean, paint: Paint) = 
        originalCanvas.drawArc(oval, startAngle, sweepAngle, useCenter, paint)
    override fun drawRoundRect(rect: RectF, rx: Float, ry: Float, paint: Paint) = 
        originalCanvas.drawRoundRect(rect, rx, ry, paint)
    override fun drawText(text: CharArray, index: Int, count: Int, x: Float, y: Float, paint: Paint) = 
        originalCanvas.drawText(text, index, count, x, y, paint)
    override fun drawText(text: String, x: Float, y: Float, paint: Paint) = 
        originalCanvas.drawText(text, x, y, paint)
    override fun drawText(text: String, start: Int, end: Int, x: Float, y: Float, paint: Paint) = 
        originalCanvas.drawText(text, start, end, x, y, paint)
    override fun drawText(text: CharSequence, start: Int, end: Int, x: Float, y: Float, paint: Paint) = 
        originalCanvas.drawText(text, start, end, x, y, paint)
}
