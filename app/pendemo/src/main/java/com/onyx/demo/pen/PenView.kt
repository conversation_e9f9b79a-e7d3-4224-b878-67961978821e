package com.onyx.demo.pen

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.widget.Toast
import com.onyx.android.sdk.base.utils.Benchmark
import com.onyx.android.sdk.base.utils.Debug
import com.onyx.android.sdk.base.data.TouchPoint
import com.onyx.android.sdk.pen.NeoBallpointPen
import com.onyx.android.sdk.pen.NeoMarkerPen
import com.onyx.android.sdk.pen.NeoPen
import com.onyx.android.sdk.pen.PenPathResult
import com.onyx.android.sdk.pen.PenResult
import java.io.InputStream
import kotlin.math.hypot

class PenView(context: Context?, attrs: AttributeSet?, defStyleAttr: Int, defStyleRes: Int) :
    View(context, attrs, defStyleAttr, defStyleRes) {

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet? = null) : this(context, attrs, -1)

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : this(context, attrs, defStyleAttr, -1)

    private var pen: NeoPen? = NeoBallpointPen.create()

    private val penResults = ArrayList<PenResult>()

    private var predictionLastPoint: TouchPoint? = null
    private var predictionCount = 0
    private var predictionDelta = 0f
    private var predictionMaxDelta = 0f

    private var backgroundBitmap: Bitmap? = null

    private var penBitmap: Bitmap? = null
    private var penCanvas: Canvas? = null

    private var paint = Paint().also {
        it.color = Color.BLACK
        it.isAntiAlias = true
        it.isDither = true
    }
    private var bitmapPaint = Paint(Paint.DITHER_FLAG)

    private val strokePoints = ArrayList<ArrayList<TouchPoint>>()
    private val strokeResults = ArrayList<ArrayList<PenResult>>()

    private var isLoggingEvents = false
    private var eventLogger: TouchEventFileLogger? = null

    fun setBackground(bitmap: Bitmap) {
        backgroundBitmap = bitmap
        clear()
    }

    fun isLogging(): Boolean {
        return isLoggingEvents
    }

    fun startLogging() {
        isLoggingEvents = true
        eventLogger?.close()
        eventLogger = TouchEventFileLogger.createLogger(context)
    }

    fun stopLogging() {
        isLoggingEvents = false
        eventLogger?.close()
        eventLogger = null
    }

    fun saveLoggerData() {
        val saved = eventLogger?.saveToExternalStorage()
        if (saved != null) {
            Toast.makeText(context, "save success: " + saved.absolutePath, Toast.LENGTH_LONG).show()
        }
    }

    fun loadLoggerDataAndRepaint(stream: InputStream) {
        clear()

        val strokes = TouchEventFileLogger.loadTouchPoints(stream)
        strokes.forEach {
            if (it.first == MotionEvent.ACTION_DOWN) {
                strokePoints.add(ArrayList())
            }
            strokePoints.last().add(it.second)
        }

        repaint()
    }

    fun clear() {
        if (penBitmap != null) {
            penBitmap!!.eraseColor(Color.WHITE)
            if (backgroundBitmap != null) {
                penCanvas?.drawBitmap(backgroundBitmap!!, 0f, 0f, bitmapPaint)
            }
        }
        penResults.clear()

        strokePoints.clear()
        strokeResults.clear()

        invalidate()
    }

    fun repaint() {
        if (penBitmap != null) {
            penBitmap!!.eraseColor(Color.WHITE)
            if (backgroundBitmap != null) {
                penCanvas?.drawBitmap(backgroundBitmap!!, 0f, 0f, bitmapPaint)
            }
        }
        penResults.clear()

        for (i in strokePoints.indices) {
            PenPathResult.resetCount()

            val stroke = strokePoints[i]
            Debug.i(javaClass, "stroke $i, size: " + stroke.size)

            Benchmark.sInstance.restart()
            for (i in 0 until stroke.size) {
                val action = when (i) {
                    0 -> MotionEvent.ACTION_DOWN
                    stroke.size - 1 -> MotionEvent.ACTION_UP
                    else -> MotionEvent.ACTION_MOVE
                }
                processTouchPoint(action, Pair(stroke[i], null), true)
            }
            Benchmark.sInstance.report("process touch point finished: " + stroke.size)

            PenPathResult.dumpCount()

            if (penResults.size != strokeResults[i].size) {
                Log.w(javaClass.simpleName, "unmatched pen result! " +
                        penResults.size + "/" + strokeResults[i].size);
            } else {
                for (j in penResults.indices) {
                    val other = strokeResults[i]
                    if (penResults[j] != other[j]) {
                        Log.w(javaClass.simpleName, "unmatched pen result: $j");
                    }
                }
            }

            Benchmark.sInstance.restart()
            for (result in penResults) {
                result.draw(penCanvas!!, paint)
            }
            Benchmark.sInstance.report("render pen result finished: " + penResults.size)

            penResults.clear()
        }

        invalidate()
    }

    fun setPen(newPen: NeoPen, newPaint: Paint) {
        pen?.destroy()
        pen = newPen
        paint = newPaint
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        penBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888).also {
            it.eraseColor(Color.WHITE)
        }
        penCanvas = Canvas(penBitmap!!)

        super.onSizeChanged(w, h, oldw, oldh)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        Benchmark.sInstance.restart()
        Benchmark.sInstance.report("onTouchEvent starting")

        if (event == null) {
            return true
        }

        if ((event.action != MotionEvent.ACTION_DOWN) &&
            (event.action != MotionEvent.ACTION_MOVE) &&
            (event.action != MotionEvent.ACTION_UP)
        ) {
            Log.d(javaClass.simpleName, "event ignored");
            return true;
        }
        eventLogger?.onEvent(event)

        if (event.action == MotionEvent.ACTION_MOVE && predictionLastPoint != null) {
            val delta = hypot(event.x - predictionLastPoint!!.x, event.y - predictionLastPoint!!.y)
            predictionDelta += delta

            predictionMaxDelta = delta.coerceAtLeast(predictionMaxDelta)
            val average = predictionDelta / predictionCount
            Log.i(javaClass.simpleName, "prediction: $delta, total: $predictionCount, average: $average, max: $predictionMaxDelta");

            predictionLastPoint = null
        }

        // TODO: no prediction
        onPointerData(Pair(event, null))

        if (event.action == MotionEvent.ACTION_UP) {
            predictionLastPoint = null
            predictionCount = 0
            predictionDelta = 0f
            predictionMaxDelta = 0f
        }
        return true
    }

    override fun onDraw(canvas: Canvas) {
        Benchmark.sInstance.report("onDraw starting: " + penResults.size)
        super.onDraw(canvas)

        canvas.drawBitmap(penBitmap!!, 0f, 0f, bitmapPaint)

        penResults.forEach {
            it.draw(canvas, paint)
        }
        penResults.clear()

        Benchmark.sInstance.report("onDraw finished.")
    }

    private fun onPointerData(pointerData: Pair<MotionEvent, TouchPoint?>) {
        processMotionEvent(pointerData)
    }

    private fun processMotionEvent(pointerData: Pair<MotionEvent, TouchPoint?>) {
        if (pointerData.first.action == MotionEvent.ACTION_DOWN) {
            strokePoints.add(ArrayList())
        }

        val point = TouchPoint(pointerData.first)
        strokePoints.last().add(point)

        processTouchPoint(pointerData.first.action,
            Pair(point, pointerData.second),
            false)
    }

    private fun processTouchPoint(action: Int, pointerData: Pair<TouchPoint, TouchPoint?>,
                                  repaint: Boolean) {
        val point = pointerData.first
        var prediction = pointerData.second
        if (prediction != null) {
            // TODO HWEstimate only estimate (x, y), but we also need pressure/size/...
            prediction.pressure = point.pressure
            prediction.size = point.size
            prediction.tiltX = point.tiltX
            prediction.tiltY = point.tiltY
            predictionLastPoint = prediction
            predictionCount++
        }

        val result = when (action) {
            MotionEvent.ACTION_DOWN -> pen?.onPenDown(point, repaint)
            MotionEvent.ACTION_MOVE -> pen?.onPenMove(List(1) { point }, prediction, repaint)
            MotionEvent.ACTION_UP -> pen?.onPenUp(point, repaint)
            else -> null
        } ?: return
        Benchmark.sInstance.report("processMotionEvent finished.")

        if (action == MotionEvent.ACTION_DOWN) {
            strokeResults.add(ArrayList())
        }
        if (result.first != null) {
            strokeResults.last().add(result.first!!)
        }

        if (repaint) {
            if (result.first != null) {
                penResults.add(result.first!!)
            }
            return
        }

        if (result.first != null && pen !is NeoMarkerPen) {
            result.first?.draw(penCanvas!!, paint)
        }

        if (action == MotionEvent.ACTION_UP) {
            if (pen is NeoMarkerPen) {
                result.first?.draw(penCanvas!!, paint)
            }
            penResults.clear()
        } else if (result.second != null) {
            penResults.add(result.second!!)
        }

        invalidate()
    }
}