package com.onyx.demo.pen

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.os.Handler
import android.os.HandlerThread
import android.os.Looper
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.widget.Toast
import com.onyx.android.sdk.base.utils.Benchmark
import com.onyx.android.sdk.base.utils.Debug
import com.onyx.android.sdk.base.data.TouchPoint
import com.onyx.android.sdk.pen.NeoBallpointPen
import com.onyx.android.sdk.pen.NeoMarkerPen
import com.onyx.android.sdk.pen.NeoPen
import com.onyx.android.sdk.pen.PenPathResult
import com.onyx.android.sdk.pen.PenResult
import java.io.InputStream
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.math.hypot

class PenView(context: Context?, attrs: AttributeSet?, defStyleAttr: Int, defStyleRes: Int) :
    View(context, attrs, defStyleAttr, defStyleRes) {

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet? = null) : this(context, attrs, -1)

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : this(context, attrs, defStyleAttr, -1)

    private var pen: NeoPen? = NeoBallpointPen.create()

    // 主线程绘制结果队列
    private val penResults = ArrayList<PenResult>()

    // 多线程相关
    private val renderThread: HandlerThread = HandlerThread("PenRenderThread").apply { start() }
    private val renderHandler: Handler = Handler(renderThread.looper)
    private val mainHandler: Handler = Handler(Looper.getMainLooper())

    // 触摸事件队列
    private val touchEventQueue = ConcurrentLinkedQueue<TouchEventData>()
    private val isProcessingTouch = AtomicBoolean(false)

    // 双缓冲位图
    private var frontBitmap: Bitmap? = null
    private var backBitmap: Bitmap? = null
    private var frontCanvas: Canvas? = null
    private var backCanvas: Canvas? = null
    private val bitmapLock = Object()

    private var predictionLastPoint: TouchPoint? = null
    private var predictionCount = 0
    private var predictionDelta = 0f
    private var predictionMaxDelta = 0f

    private var backgroundBitmap: Bitmap? = null

    private var penBitmap: Bitmap? = null
    private var penCanvas: Canvas? = null

    private var paint = Paint().also {
        it.color = Color.BLACK
        it.isAntiAlias = true
        it.isDither = true
    }
    private var bitmapPaint = Paint(Paint.DITHER_FLAG)

    private val strokePoints = ArrayList<ArrayList<TouchPoint>>()
    private val strokeResults = ArrayList<ArrayList<PenResult>>()

    private var isLoggingEvents = false
    private var eventLogger: TouchEventFileLogger? = null

    // 性能监控器
    private val performanceMonitor = PenPerformanceMonitor()

    // 绘制日志标签
    companion object {
        private const val DRAW_LOG_TAG = "PenDrawLog"
    }

    // 触摸事件数据类
    data class TouchEventData(
        val action: Int,
        val point: TouchPoint,
        val prediction: TouchPoint?,
        val timestamp: Long = System.currentTimeMillis()
    )

    fun setPenBackground(bitmap: Bitmap) {
        backgroundBitmap = bitmap

        // 在后台线程更新背景
        renderHandler.post {
            synchronized(bitmapLock) {
                frontCanvas?.drawBitmap(bitmap, 0f, 0f, bitmapPaint)
                backCanvas?.drawBitmap(bitmap, 0f, 0f, bitmapPaint)
            }
            mainHandler.post {
                invalidate()
            }
        }
    }

    fun isLogging(): Boolean {
        return isLoggingEvents
    }

    fun startLogging() {
        isLoggingEvents = true
        eventLogger?.close()
        eventLogger = TouchEventFileLogger.createLogger(context)
    }

    fun stopLogging() {
        isLoggingEvents = false
        eventLogger?.close()
        eventLogger = null
    }

    fun saveLoggerData() {
        val saved = eventLogger?.saveToExternalStorage()
        if (saved != null) {
            Toast.makeText(context, "save success: " + saved.absolutePath, Toast.LENGTH_LONG).show()
        }
    }

    fun loadLoggerDataAndRepaint(stream: InputStream) {
        clear()

        val strokes = TouchEventFileLogger.loadTouchPoints(stream)
        strokes.forEach {
            if (it.first == MotionEvent.ACTION_DOWN) {
                strokePoints.add(ArrayList())
            }
            strokePoints.last().add(it.second)
        }

        repaint()
    }

    fun clear() {
        // 清空触摸事件队列
        touchEventQueue.clear()

        synchronized(bitmapLock) {
            frontBitmap?.eraseColor(Color.WHITE)
            backBitmap?.eraseColor(Color.WHITE)

            if (backgroundBitmap != null) {
                frontCanvas?.drawBitmap(backgroundBitmap!!, 0f, 0f, bitmapPaint)
                backCanvas?.drawBitmap(backgroundBitmap!!, 0f, 0f, bitmapPaint)
            }

            // 保持兼容性
            if (penBitmap != null) {
                penBitmap!!.eraseColor(Color.WHITE)
                if (backgroundBitmap != null) {
                    penCanvas?.drawBitmap(backgroundBitmap!!, 0f, 0f, bitmapPaint)
                }
            }
        }

        penResults.clear()
        strokePoints.clear()
        strokeResults.clear()

        invalidate()
    }

    fun repaint() {
        // 在后台线程执行重绘
        renderHandler.post {
            repaintInBackground()
        }
    }

    private fun repaintInBackground() {
        synchronized(bitmapLock) {
            backBitmap?.eraseColor(Color.WHITE)
            if (backgroundBitmap != null) {
                backCanvas?.drawBitmap(backgroundBitmap!!, 0f, 0f, bitmapPaint)
            }

            val canvas = backCanvas ?: return

            for (i in strokePoints.indices) {
                PenPathResult.resetCount()

                val stroke = strokePoints[i]
                Debug.i(javaClass, "stroke $i, size: " + stroke.size)

                Benchmark.sInstance.restart()
                val tempResults = mutableListOf<PenResult>()

                for (j in 0 until stroke.size) {
                    val action = when (j) {
                        0 -> MotionEvent.ACTION_DOWN
                        stroke.size - 1 -> MotionEvent.ACTION_UP
                        else -> MotionEvent.ACTION_MOVE
                    }

                    val result = when (action) {
                        MotionEvent.ACTION_DOWN -> pen?.onPenDown(stroke[j], true)
                        MotionEvent.ACTION_MOVE -> pen?.onPenMove(List(1) { stroke[j] }, null, true)
                        MotionEvent.ACTION_UP -> pen?.onPenUp(stroke[j], true)
                        else -> null
                    }

                    result?.first?.let { tempResults.add(it) }
                }
                Benchmark.sInstance.report("process touch point finished: " + stroke.size)

                PenPathResult.dumpCount()

                Benchmark.sInstance.restart()
                for (result in tempResults) {
                    result.draw(canvas, paint)
                }
                Benchmark.sInstance.report("render pen result finished: " + tempResults.size)
            }

            // 交换缓冲区并更新显示
            swapBuffers()
        }
    }

    fun setPen(newPen: NeoPen, newPaint: Paint) {
        pen?.destroy()
        pen = newPen
        paint = newPaint
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        synchronized(bitmapLock) {
            // 创建双缓冲位图
            frontBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888).also {
                it.eraseColor(Color.WHITE)
            }
            backBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888).also {
                it.eraseColor(Color.WHITE)
            }

            // 使用LoggingCanvas包装原始Canvas以记录绘制操作
            frontCanvas = LoggingCanvas(Canvas(frontBitmap!!))
            backCanvas = LoggingCanvas(Canvas(backBitmap!!))

            // 保持原有的penBitmap兼容性
            penBitmap = frontBitmap
            penCanvas = frontCanvas
        }

        super.onSizeChanged(w, h, oldw, oldh)
    }

    /**
     * 后台渲染线程处理触摸事件
     */
    private fun processRenderQueue() {
        if (!isProcessingTouch.compareAndSet(false, true)) {
            return // 已经在处理中
        }

        performanceMonitor.updateProcessingState(true)

        renderHandler.post {
            val startTime = performanceMonitor.recordRenderStart()

            try {
                val eventsToProcess = mutableListOf<TouchEventData>()

                // 批量获取触摸事件
                while (touchEventQueue.isNotEmpty() && eventsToProcess.size < 10) {
                    touchEventQueue.poll()?.let { eventsToProcess.add(it) }
                }

                performanceMonitor.updateQueueSize(touchEventQueue.size)

                if (eventsToProcess.isNotEmpty()) {
                    processRenderEvents(eventsToProcess)
                }
            } finally {
                performanceMonitor.recordRenderEnd(startTime)
                performanceMonitor.updateProcessingState(false)
                isProcessingTouch.set(false)

                // 检查是否还有待处理的事件
                if (touchEventQueue.isNotEmpty()) {
                    processRenderQueue()
                }
            }
        }
    }

    /**
     * 在后台线程处理渲染事件
     */
    private fun processRenderEvents(events: List<TouchEventData>) {
        synchronized(bitmapLock) {
            val canvas = backCanvas ?: return

            for (event in events) {
                val result = when (event.action) {
                    MotionEvent.ACTION_DOWN -> pen?.onPenDown(event.point, false)
                    MotionEvent.ACTION_MOVE -> pen?.onPenMove(List(1) { event.point }, event.prediction, false)
                    MotionEvent.ACTION_UP -> pen?.onPenUp(event.point, false)
                    else -> null
                } ?: continue

                // 直接绘制到后台Canvas
                if (result.first != null && pen !is NeoMarkerPen) {
                    result.first?.draw(canvas, paint)
                }

                // 对于MarkerPen，在ACTION_UP时绘制
                if (event.action == MotionEvent.ACTION_UP && pen is NeoMarkerPen) {
                    result.first?.draw(canvas, paint)
                }

                // 保存预测结果到主线程队列
                if (result.second != null) {
                    mainHandler.post {
                        penResults.add(result.second!!)
                        invalidate()
                    }
                }
            }

            // 交换缓冲区
            swapBuffers()
        }
    }

    /**
     * 交换前后缓冲区
     */
    private fun swapBuffers() {
        val tempBitmap = frontBitmap
        val tempCanvas = frontCanvas

        frontBitmap = backBitmap
        frontCanvas = backCanvas
        backBitmap = tempBitmap
        backCanvas = tempCanvas

        // 更新兼容性引用
        penBitmap = frontBitmap
        penCanvas = frontCanvas

        // 通知主线程更新显示
        mainHandler.post {
            invalidate()
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        Benchmark.sInstance.restart()
        Benchmark.sInstance.report("onTouchEvent starting")

        if (event == null) {
            return true
        }

        if ((event.action != MotionEvent.ACTION_DOWN) &&
            (event.action != MotionEvent.ACTION_MOVE) &&
            (event.action != MotionEvent.ACTION_UP)
        ) {
            Log.d(javaClass.simpleName, "event ignored");
            return true;
        }

        // 记录触摸事件
        performanceMonitor.recordTouchEvent()
        eventLogger?.onEvent(event)

        if (event.action == MotionEvent.ACTION_MOVE && predictionLastPoint != null) {
            val delta = hypot(event.x - predictionLastPoint!!.x, event.y - predictionLastPoint!!.y)
            predictionDelta += delta

            predictionMaxDelta = delta.coerceAtLeast(predictionMaxDelta)
            val average = predictionDelta / predictionCount
            Log.i(javaClass.simpleName, "prediction: $delta, total: $predictionCount, average: $average, max: $predictionMaxDelta");

            predictionLastPoint = null
        }

        // 使用多线程处理触摸事件
        onPointerDataAsync(Pair(event, null))

        if (event.action == MotionEvent.ACTION_UP) {
            predictionLastPoint = null
            predictionCount = 0
            predictionDelta = 0f
            predictionMaxDelta = 0f
        }
        return true
    }

    /**
     * 异步处理触摸数据
     */
    private fun onPointerDataAsync(pointerData: Pair<MotionEvent, TouchPoint?>) {
        val event = pointerData.first
        val point = TouchPoint(event)

        // 添加到数据结构（保持兼容性）
        if (event.action == MotionEvent.ACTION_DOWN) {
            strokePoints.add(ArrayList())
        }
        strokePoints.lastOrNull()?.add(point)

        // 创建触摸事件数据并加入队列
        val touchEventData = TouchEventData(
            action = event.action,
            point = point,
            prediction = pointerData.second
        )

        touchEventQueue.offer(touchEventData)

        // 触发后台处理
        processRenderQueue()
    }

    override fun onDraw(canvas: Canvas) {
        Benchmark.sInstance.report("onDraw starting: " + penResults.size)
        super.onDraw(canvas)

        // 记录帧绘制
        performanceMonitor.recordFrame()

        synchronized(bitmapLock) {
            // 绘制前台缓冲区位图
            frontBitmap?.let { bitmap ->
                canvas.drawBitmap(bitmap, 0f, 0f, bitmapPaint)
            }
        }

        // 绘制预测结果（实时反馈）
        penResults.forEach {
            it.draw(canvas, paint)
        }
        penResults.clear()

        Benchmark.sInstance.report("onDraw finished.")
    }

    private fun onPointerData(pointerData: Pair<MotionEvent, TouchPoint?>) {
        processMotionEvent(pointerData)
    }

    private fun processMotionEvent(pointerData: Pair<MotionEvent, TouchPoint?>) {
        if (pointerData.first.action == MotionEvent.ACTION_DOWN) {
            strokePoints.add(ArrayList())
        }

        val point = TouchPoint(pointerData.first)
        strokePoints.last().add(point)

        processTouchPoint(pointerData.first.action,
            Pair(point, pointerData.second),
            false)
    }

    private fun processTouchPoint(action: Int, pointerData: Pair<TouchPoint, TouchPoint?>,
                                  repaint: Boolean) {
        val point = pointerData.first
        var prediction = pointerData.second
        if (prediction != null) {
            // TODO HWEstimate only estimate (x, y), but we also need pressure/size/...
            prediction.pressure = point.pressure
            prediction.size = point.size
            prediction.tiltX = point.tiltX
            prediction.tiltY = point.tiltY
            predictionLastPoint = prediction
            predictionCount++
        }

        val result = when (action) {
            MotionEvent.ACTION_DOWN -> pen?.onPenDown(point, repaint)
            MotionEvent.ACTION_MOVE -> pen?.onPenMove(List(1) { point }, prediction, repaint)
            MotionEvent.ACTION_UP -> pen?.onPenUp(point, repaint)
            else -> null
        } ?: return
        Benchmark.sInstance.report("processMotionEvent finished.")

        if (action == MotionEvent.ACTION_DOWN) {
            strokeResults.add(ArrayList())
        }
        if (result.first != null) {
            strokeResults.last().add(result.first!!)
        }

        if (repaint) {
            if (result.first != null) {
                penResults.add(result.first!!)
            }
            return
        }

        if (result.first != null && pen !is NeoMarkerPen) {
            result.first?.draw(penCanvas!!, paint)
        }

        if (action == MotionEvent.ACTION_UP) {
            if (pen is NeoMarkerPen) {
                result.first?.draw(penCanvas!!, paint)
            }
            penResults.clear()
        } else if (result.second != null) {
            penResults.add(result.second!!)
        }

        invalidate()
    }

    /**
     * 清理资源
     */
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()

        // 停止渲染线程
        renderHandler.removeCallbacksAndMessages(null)
        renderThread.quitSafely()

        // 清理位图资源
        synchronized(bitmapLock) {
            frontBitmap?.recycle()
            backBitmap?.recycle()
            frontBitmap = null
            backBitmap = null
            frontCanvas = null
            backCanvas = null
        }

        // 清理队列
        touchEventQueue.clear()
        penResults.clear()
    }



    /**
     * 开始性能监控
     */
    fun startPerformanceMonitoring() {
        performanceMonitor.startMonitoring()
    }

    /**
     * 停止性能监控
     */
    fun stopPerformanceMonitoring() {
        performanceMonitor.stopMonitoring()
    }

    /**
     * 获取当前绘制性能统计
     */
    fun getPerformanceStats(): String {
        val stats = performanceMonitor.getPerformanceStats()
        return """
            Queue size: ${touchEventQueue.size}
            Processing: ${isProcessingTouch.get()}
            Render thread alive: ${renderThread.isAlive}
            Touch events: ${stats.touchEvents}
            Render events: ${stats.renderEvents}
            Frames: ${stats.frames}
            Avg render time: ${"%.2f".format(stats.averageRenderTime)}ms
            Max render time: ${stats.maxRenderTime}ms
            Queue overflows: ${stats.queueOverflows}
        """.trimIndent()
    }

    /**
     * 获取详细性能统计
     */
    fun getDetailedPerformanceStats(): PenPerformanceMonitor.PerformanceStats {
        return performanceMonitor.getPerformanceStats()
    }
}