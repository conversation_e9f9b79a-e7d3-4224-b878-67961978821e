<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="penModel"
            type="com.onyx.demo.pen.PenViewModel" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context="com.onyx.demo.pen.PenActivity">

        <com.onyx.demo.pen.FlowLayout
            android:id="@+id/layout_function"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <Button
                android:id="@+id/button_background"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Change Background"/>

            <Button
                android:id="@+id/button_toggle_logging"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{penModel.loggingButtonText}"
                android:onClick="@{() -> penModel.toggleLogging()}"/>

            <Button
                android:id="@+id/button_save_logger"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Save logger data"
                android:onClick="@{() -> penModel.saveLoggerData()}"/>

            <Button
                android:id="@+id/button_load_logger"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Load logger data"/>

            <Button
                android:id="@+id/button_erase"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Erase"
                android:onClick="@{() -> penModel.clear()}" />

            <Button
                android:id="@+id/button_repaint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Repaint"
                android:onClick="@{() -> penModel.repaint()}" />

            <Button
                android:id="@+id/button_ballpoint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ballpoint"
                android:onClick="@{() -> penModel.useBallpointPen()}"  />

            <Button
                android:id="@+id/button_square"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Square"
                android:onClick="@{() -> penModel.useSquarePen()}"  />

            <Button
                android:id="@+id/button_fountain"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Fountain"
                android:onClick="@{() -> penModel.useFountainPen()}"  />

            <Button
                android:id="@+id/button_xiuli"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="XiuLi"
                android:onClick="@{() -> penModel.useXiuLiPen()}"  />

            <Button
                android:id="@+id/button_marker"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Marker"
                android:onClick="@{() -> penModel.useMarkerPen()}"  />

            <Button
                android:id="@+id/button_bottom_marker"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Bottom Marker"
                android:onClick="@{() -> penModel.useBottomMarkerPen()}"  />

            <Button
                android:id="@+id/button_pencil"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Pencil"
                android:onClick="@{() -> penModel.usePencilPen()}" />

            <Button
                android:id="@+id/button_pencil_new"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Pencil New"
                android:onClick="@{() -> penModel.usePencilPenNew()}" />

            <Button
                android:id="@+id/button_perf_test"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Performance Test"
                android:textSize="12sp" />

            <Button
                android:id="@+id/button_stress_test"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Stress Test"
                android:textSize="12sp" />

        </com.onyx.demo.pen.FlowLayout>

        <com.onyx.demo.pen.FlowLayout
            android:id="@+id/layout_config"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/layout_function"
            android:background="@color/white"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text='@{"Pen Width: " + penModel.penWidth}'
                    android:textColor="@color/black"
                    android:textSize="20sp"
                    android:layout_gravity="center"/>
                <SeekBar
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:min="1"
                    android:max="50"
                    android:progress="@={penModel.penWidth}"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text='@{"Pressure Sensitivity: " + penModel.pressureSensitivity}'
                    android:textColor="@color/black"
                    android:textSize="20sp"
                    android:layout_gravity="center"/>
                <SeekBar
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:max="100"
                    android:progress="@={penModel.pressureSensitivity}"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text='@{"Velocity Sensitivity: " + penModel.velocitySensitivity}'
                    android:textColor="@color/black"
                    android:textSize="20sp"
                    android:layout_gravity="center"/>
                <SeekBar
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:max="10"
                    android:progress="@={penModel.velocitySensitivity}"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text='@{"Spacing: " + penModel.brushSpacing}'
                    android:textColor="@color/black"
                    android:textSize="20sp"
                    android:layout_gravity="center"/>
                <SeekBar
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:max="10"
                    android:progress="@={penModel.brushSpacing}"/>
            </LinearLayout>

        </com.onyx.demo.pen.FlowLayout>

        <com.onyx.demo.pen.PenView
            android:id="@+id/pen_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/layout_config" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true">

            <TextView
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:text="@={penModel.motionEvent}"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Display Rate: " />

            <TextView
                android:id="@+id/text_view_display_rate"
                android:layout_width="100dp"
                android:layout_height="wrap_content" />
        </LinearLayout>

    </RelativeLayout>
</layout>