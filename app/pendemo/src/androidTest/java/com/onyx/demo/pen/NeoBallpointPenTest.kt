package com.onyx.demo.pen

import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.onyx.android.sdk.pen.NeoBallpointInkPen
import com.onyx.android.sdk.pen.PenPathResult
import com.onyx.android.sdk.pen.PenPointResult
import com.onyx.android.sdk.pen.PenResult
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class NeoBallpointPenTest: PenTestBase() {

    @Test
    fun testBallpointPenFastMode() {
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext

        val stream = appContext.assets.open("touch_points_1718181544105.json")
        Assert.assertNotNull(stream)
        val strokes = loadStrokes(appContext.assets.open("touch_points_1718181544105.json"))

        NeoBallpointInkPen.create(NeoBallpointInkPen.defaultPenConfig().also {
            it.width = 5.0f
            it.fastMode = false
        })!!.apply {
            val list = ArrayList<PenResult>()
            strokes.forEach { stroke ->
                list.addAll(computePenResult(this, stroke))
            }
            PenPathResult.dumpCount()
        }

        NeoBallpointInkPen.create(NeoBallpointInkPen.defaultPenConfig().also {
            it.width = 5.0f
            it.fastMode = true
        })!!.apply {
            val list = ArrayList<PenResult>()
            strokes.forEach { stroke ->
                list.addAll(computePenResult(this, stroke))
            }
            PenPointResult.dumpCount()
        }
    }
}