package com.onyx.demo.pen

import android.view.MotionEvent
import com.onyx.android.sdk.base.data.TouchPoint
import com.onyx.android.sdk.base.utils.Debug
import com.onyx.android.sdk.pen.NeoPen
import com.onyx.android.sdk.pen.PenResult
import java.io.InputStream

open class PenTestBase {

    fun loadStrokes(stream: InputStream): ArrayList<ArrayList<TouchPoint>> {
        val points = TouchEventFileLogger.loadTouchPoints(stream)

        var pointCount = 0

        val strokes = ArrayList<ArrayList<TouchPoint>>()
        points.forEach {
            if (it.first == MotionEvent.ACTION_DOWN) {
                strokes.add(ArrayList())
            }
            strokes.last().add(it.second)
            pointCount++
        }
        Debug.i(javaClass, "strokes: " + strokes.size +", points: $pointCount")
        return strokes
    }

    fun computePenResult(pen: <PERSON>Pen, it: ArrayList<TouchPoint>): ArrayList<PenResult> {
        val list = ArrayList<PenResult>()

        if (it.size < 2) {
            return arrayListOf()
        }

        var result: Pair<PenResult?, PenResult?>?
        result = pen.onPenDown(it.first(), true)
        if (result.first != null) {
            list.add(result.first!!)
        }

        if (it.size > 2) {
            result = pen.onPenMove(it.subList(1, it.size - 1), null, true)
            if (result.first != null) {
                list.add(result.first!!)
            }
        }

        result = pen.onPenUp(it.first(), true)
        if (result.first != null) {
            list.add(result.first!!)
        }

        return list
    }
}