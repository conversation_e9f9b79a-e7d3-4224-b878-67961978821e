package com.onyx.demo.pen

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.view.MotionEvent
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.onyx.android.sdk.base.data.TouchPoint
import com.onyx.android.sdk.base.utils.Benchmark
import com.onyx.android.sdk.base.utils.Debug
import com.onyx.android.sdk.pen.NeoFountainPen
import com.onyx.android.sdk.pen.NeoPen
import com.onyx.android.sdk.pen.NeoPenConfig
import com.onyx.android.sdk.pen.NeoPencilPen
import com.onyx.android.sdk.pen.PenPathResult
import com.onyx.android.sdk.pen.PenResult
import org.junit.Assert.*
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Instrumented test, which will execute on an Android device.
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
@RunWith(AndroidJUnit4::class)
class PenRepaintTest {

    private fun createFountainPen(): NeoPen {
        val config = NeoPenConfig().also {
            it.width = 10.0f // use larger pen width for 13" mate pad
            it.maxTouchPressure = 1.0f
            it.pressureSensitivity = 1f
            it.velocitySensitivity = 0.3f
        }
        val pen = NeoFountainPen.create(config)
        assertNotNull(pen)
        return pen!!
    }

    private fun createPencilPen(context: Context): NeoPen {
        val config = NeoPenConfig().also {
            it.width = 10.0f
            it.maxTouchPressure = 1.0f
            it.tiltEnabled = false
            it.tiltScale = 2.0f
            it.brushSpacing = 0.25f
            it.brushShapes = mutableListOf(BitmapFactory.decodeResource(context.resources, com.onyx.android.sdk.pen.R.drawable.pencil))
        }
        val pen = NeoPencilPen.create(config)
        assertNotNull(pen)
        return pen!!
    }

    @Test
    fun testPensBenchmark() {
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext

        // pen and benchmark data on MatePad pro 13
        val penData = ArrayList<Triple<NeoPen, Int, Int>>()
        penData.add(Triple(createFountainPen(), 75, 135))
        penData.add(Triple(createPencilPen(appContext), 75, 4000))

        val stream = appContext.assets.open("touch_points_1713946263905.json")

        assertNotNull(stream)
        val points = TouchEventFileLogger.loadTouchPoints(stream)

        var pointCount = 0

        val strokes = ArrayList<ArrayList<TouchPoint>>()
        points.forEach {
            if (it.first == MotionEvent.ACTION_DOWN) {
                strokes.add(ArrayList())
            }
            strokes.last().add(it.second)
            pointCount++
        }
        Debug.i(javaClass, "strokes: " + strokes.size +", points: $pointCount")

        val bitmap = Bitmap.createBitmap(1600, 2560, Bitmap.Config.ARGB_8888)

        penData.forEach { (pen, expectedPenDuration, expectedRenderDuration) ->
            Debug.i(javaClass, "current pen: $pen")

            var penDuration = 0L
            var renderDuration = 0L

            val loop = 5

            for (i in 1..loop) {
                val list = ArrayList<PenResult>()

                val penBenchmark = Benchmark()
                strokes.forEach {
                    if (it.size < 2) {
                        return@forEach
                    }

                    var result: Pair<PenResult?, PenResult?>?
                    result = pen.onPenDown(it.first(), true)
                    if (result.first != null) {
                        list.add(result.first!!)
                    }

                    if (it.size > 2) {
                        result = pen.onPenMove(it.subList(1, it.size - 1), null, true)
                        if (result.first != null) {
                            list.add(result.first!!)
                        }
                    }

                    result = pen.onPenUp(it.first(), true)
                    if (result.first != null) {
                        list.add(result.first!!)
                    }
                }
                penDuration += penBenchmark.duration()

                bitmap.eraseColor(Color.WHITE)
                val canvas = Canvas(bitmap)
                val paint = Paint().also {
                    it.isAntiAlias = true
                    it.isDither = true
                    it.style = Paint.Style.FILL
                }

                val renderBenchmark = Benchmark()
                list.forEach {
                    it.draw(canvas, paint)
                }
                renderDuration += renderBenchmark.duration()
            }

            val averagePenDuration = penDuration / loop
            Debug.i(javaClass, "pen process duration: $averagePenDuration (ms) over $loop loop")
            assertTrue("pen process duration: $averagePenDuration (ms) over $loop loop", averagePenDuration < expectedPenDuration)

            val averageRenderDuration = renderDuration / loop
            Debug.i(javaClass, "pen render duration: $averageRenderDuration (ms) over $loop loop")
            assertTrue("pen render duration: $averageRenderDuration (ms) over $loop loop", averageRenderDuration < expectedRenderDuration)
        }
    }

    @Test
    fun testPencilRepaint() {
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext

        val stream = appContext.assets.open("touch_points_1713946263905.json")
        assertNotNull(stream)
        val points = TouchEventFileLogger.loadTouchPoints(stream)

        val config = NeoPencilPen.defaultPenConfig().also {
            it.width = 10.0f
            it.maxTouchPressure = 1.0f
            it.tiltEnabled = false
            it.tiltScale = 2.0f
            it.brushSpacing = 0.25f
            it.brushShapes = listOf(BitmapFactory.decodeResource(appContext.resources, com.onyx.android.sdk.pen.R.drawable.pencil)).toMutableList()
        }
        val pen = NeoPencilPen.create(config)
        assertNotNull(pen)

        val list = ArrayList<PenResult>()

        Benchmark.sInstance.restart()
        var count = 0
        for (i in 1..1) {
            points.forEach {
                count++
                val result = when (it.first) {
                    MotionEvent.ACTION_DOWN -> pen!!.onPenDown(it.second, true)
                    MotionEvent.ACTION_MOVE -> pen!!.onPenMove(listOf(it.second), null, true)
                    MotionEvent.ACTION_UP -> pen!!.onPenUp(it.second, true)
                    else -> null
                }
                assertNotNull(result)

                if (result!!.first != null) {
                    list.add(result!!.first!!)
                }
            }
        }
        Benchmark.sInstance.report("process pencil pen finished: $count")

        val bitmap = Bitmap.createBitmap(1600, 2560, Bitmap.Config.ARGB_8888)
        bitmap.eraseColor(Color.WHITE)

        val canvas = Canvas(bitmap)
        val paint = Paint()

        Benchmark.sInstance.restart()
        count = 0
        list.forEach {
            count++
            it.draw(canvas, paint)
        }
        Benchmark.sInstance.report("draw pencil pen finished: $count")
    }

    @Test
    fun testFountainPenRepaint() {
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext

        val stream = appContext.assets.open("touch_points_1718181544105.json")
        assertNotNull(stream)
        val points = TouchEventFileLogger.loadTouchPoints(stream)

        val config = NeoFountainPen.defaultPenConfig().also {
            it.width = 10.0f // use larger pen width for 13" mate pad
            it.maxTouchPressure = 1.0f
            it.pressureSensitivity = 1f
            it.velocitySensitivity = 0.3f
        }
        val pen = NeoFountainPen.create(config)
        assertNotNull(pen)

        val list = ArrayList<PenResult>()

        Benchmark.sInstance.restart()
        var pointCount = 0
        var strokeCount = 0
        for (i in 1..1) {
            points.forEach {
                pointCount++
                if (it.first == MotionEvent.ACTION_UP) {
                    strokeCount++
                }
                val result = when (it.first) {
                    MotionEvent.ACTION_DOWN -> pen!!.onPenDown(it.second, true)
                    MotionEvent.ACTION_MOVE -> pen!!.onPenMove(listOf(it.second), null, true)
                    MotionEvent.ACTION_UP -> pen!!.onPenUp(it.second, true)
                    else -> null
                }
                assertNotNull(result)

                if (result!!.first != null) {
                    list.add(result!!.first!!)
                }
            }
        }
        Benchmark.sInstance.report("process pencil pen finished: $strokeCount/$pointCount")
        PenPathResult.dumpCount()

        val bitmap = Bitmap.createBitmap(1600, 2560, Bitmap.Config.ARGB_8888)
        bitmap.eraseColor(Color.WHITE)

        val canvas = Canvas(bitmap)

        val paint = Paint().also {
            it.isAntiAlias = true
            it.isDither = true
            it.style = Paint.Style.FILL
        }

        Benchmark.sInstance.restart()
        pointCount = 0
        list.forEach {
            pointCount++
            it.draw(canvas, paint)
        }
        Benchmark.sInstance.report("draw pencil pen finished: $strokeCount/$pointCount")
    }

    private fun renderStroke(pen: NeoPen,
                             points: ArrayList<Pair<Int, TouchPoint>>,
                             repaint: Boolean): ArrayList<PenResult> {
        val list = ArrayList<PenResult>()

        points.forEach {
            val result = when (it.first) {
                MotionEvent.ACTION_DOWN -> pen.onPenDown(it.second, repaint)
                MotionEvent.ACTION_MOVE -> pen.onPenMove(listOf(it.second), null, repaint)
                MotionEvent.ACTION_UP -> pen.onPenUp(it.second, repaint)
                else -> null
            }
            assertNotNull(result)

            if (result!!.first != null) {
                list.add(result!!.first!!)
            }
        }

        return list
    }

    @Test
    fun verifyRepaintResult() {
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext

        val stream = appContext.assets.open("touch_points_1718181544105.json")
        assertNotNull(stream)
        val points = TouchEventFileLogger.loadTouchPoints(stream)

        val config = NeoFountainPen.defaultPenConfig().also {
            it.width = 10.0f // use larger pen width for 13" mate pad
            it.maxTouchPressure = 1.0f
            it.pressureSensitivity = 1f
            it.velocitySensitivity = 0.3f
        }
        val pen = NeoFountainPen.create(config)!!

        val realTimeList = renderStroke(pen, points, false)
        val repaintList = renderStroke(pen, points, true)

        assertEquals(realTimeList.size, repaintList.size)
        for (i in realTimeList.indices) {
            assert(realTimeList[i] == repaintList[i])
        }
    }

    @Test
    fun verifyFountainPenRepaintResult() {
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext

        val stream = appContext.assets.open("touch_points_1718181544105.json")
        assertNotNull(stream)
        val points = TouchEventFileLogger.loadTouchPoints(stream)

        val config = NeoFountainPen.defaultPenConfig().also {
            it.width = 10.0f // use larger pen width for 13" mate pad
            it.maxTouchPressure = 1.0f
            it.pressureSensitivity = 1f
            it.velocitySensitivity = 0.3f
        }
        val pen = NeoFountainPen.create(config)!!

        Benchmark.sInstance.restart()
        val realTimeList = renderStroke(pen, points, false)
        val realTimeDuration = Benchmark.sInstance.duration()
        assertTrue("realtime duration: $realTimeDuration", realTimeDuration in 2200..2400)

        Benchmark.sInstance.restart()
        val repaintList = renderStroke(pen, points, true)
        val repaintDuration = Benchmark.sInstance.duration()
        assertTrue("repaint duration: $repaintDuration", repaintDuration in 1000..1200)

        assertEquals(realTimeList.size, repaintList.size)
        for (i in realTimeList.indices) {
            assert(realTimeList[i] == repaintList[i])
        }
    }

    @Test
    fun verifyPencilPenRepaintResult() {
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext

        val stream = appContext.assets.open("touch_points_1718181544105.json")
        assertNotNull(stream)
        val points = TouchEventFileLogger.loadTouchPoints(stream)

        val config = NeoPencilPen.defaultPenConfig().also {
            it.width = 10.0f
            it.maxTouchPressure = 1.0f
            it.tiltEnabled = false
            it.tiltScale = 2.0f
            it.brushSpacing = 0.25f
            it.brushShapes = mutableListOf(BitmapFactory.decodeResource(appContext.resources, com.onyx.android.sdk.pen.R.drawable.pencil))
        }
        val pen = NeoPencilPen.create(config)!!

        Benchmark.sInstance.restart()
        val realTimeList = renderStroke(pen, points, false)
        val realTimeDuration = Benchmark.sInstance.duration()
        assertTrue("realtime duration: $realTimeDuration", realTimeDuration in 2200..2600)

        Benchmark.sInstance.restart()
        val repaintList = renderStroke(pen, points, true)
        val repaintDuration = Benchmark.sInstance.duration()
        assertTrue("repaint duration: $repaintDuration", repaintDuration in 700..900)

        assertEquals(realTimeList.size, repaintList.size)
        for (i in realTimeList.indices) {
            assert(realTimeList[i] == repaintList[i])
        }
    }
}