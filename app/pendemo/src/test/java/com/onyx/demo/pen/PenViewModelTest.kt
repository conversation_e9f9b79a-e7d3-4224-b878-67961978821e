package com.onyx.demo.pen

import android.content.Context
import android.graphics.Paint
import com.onyx.android.sdk.pen.NeoPen
import com.onyx.android.sdk.pen.NeoPenConfig
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.junit.MockitoJUnitRunner

/**
 * PenViewModel的单元测试
 * 验证MVVM架构重构后的正确性
 */
@RunWith(MockitoJUnitRunner::class)
class PenViewModelTest {

    @Mock
    private lateinit var mockContext: Context

    @Mock
    private lateinit var mockPenViewContract: PenViewContract

    private lateinit var penViewModel: PenViewModel
    private lateinit var penConfig: NeoPenConfig

    @Before
    fun setup() {
        penConfig = NeoPenConfig()
        penViewModel = PenViewModel(mockContext, penConfig)
        penViewModel.setPenViewContract(mockPenViewContract)
    }

    @Test
    fun testSetPenViewContract() {
        // Given
        val newMockContract = mock(PenViewContract::class.java)
        
        // When
        penViewModel.setPenViewContract(newMockContract)
        
        // Then
        // 验证新的contract被设置（通过调用方法来验证）
        penViewModel.clear()
        verify(newMockContract).clear()
    }

    @Test
    fun testClearCallsViewContract() {
        // When
        penViewModel.clear()
        
        // Then
        verify(mockPenViewContract).clear()
    }

    @Test
    fun testRepaintCallsViewContract() {
        // When
        penViewModel.repaint()
        
        // Then
        verify(mockPenViewContract).repaint()
    }

    @Test
    fun testSaveLoggerDataCallsViewContract() {
        // When
        penViewModel.saveLoggerData()
        
        // Then
        verify(mockPenViewContract).saveLoggerData()
    }

    @Test
    fun testToggleLoggingWhenNotLogging() {
        // Given
        `when`(mockPenViewContract.isLogging()).thenReturn(false)
        `when`(mockContext.getString(R.string.stop_logging)).thenReturn("Stop Logging")
        
        // When
        penViewModel.toggleLogging()
        
        // Then
        verify(mockPenViewContract).startLogging()
        verify(mockPenViewContract, never()).stopLogging()
    }

    @Test
    fun testToggleLoggingWhenLogging() {
        // Given
        `when`(mockPenViewContract.isLogging()).thenReturn(true)
        `when`(mockContext.getString(R.string.start_logging)).thenReturn("Start Logging")
        
        // When
        penViewModel.toggleLogging()
        
        // Then
        verify(mockPenViewContract).stopLogging()
        verify(mockPenViewContract, never()).startLogging()
    }

    @Test
    fun testUseBallpointPenUpdatesPenType() {
        // When
        penViewModel.useBallpointPen()
        
        // Then
        assert(penViewModel.getCurrentPenType() == PenType.Ballpoint)
        verify(mockPenViewContract).setPen(any(NeoPen::class.java), any(Paint::class.java))
    }

    @Test
    fun testUseFountainPenUpdatesPenType() {
        // When
        penViewModel.useFountainPen()
        
        // Then
        assert(penViewModel.getCurrentPenType() == PenType.Fountain)
        verify(mockPenViewContract).setPen(any(NeoPen::class.java), any(Paint::class.java))
    }

    @Test
    fun testUseXiuLiPenUpdatesPenType() {
        // When
        penViewModel.useXiuLiPen()
        
        // Then
        assert(penViewModel.getCurrentPenType() == PenType.XiuLi)
        verify(mockPenViewContract).setPen(any(NeoPen::class.java), any(Paint::class.java))
    }

    @Test
    fun testPenWidthChangeTriggersConfigUpdate() {
        // Given
        val newWidth = 25
        
        // When
        penViewModel.penWidth.set(newWidth)
        
        // Then
        assert(penViewModel.getCurrentPenConfig().width == newWidth.toFloat())
        verify(mockPenViewContract).setPen(any(NeoPen::class.java), any(Paint::class.java))
    }

    @Test
    fun testPressureSensitivityChangeTriggersConfigUpdate() {
        // Given
        val newSensitivity = 75
        
        // When
        penViewModel.pressureSensitivity.set(newSensitivity)
        
        // Then
        val expectedValue = newSensitivity.toFloat() / 100 // pressureSensitivityMultiplier
        assert(penViewModel.getCurrentPenConfig().pressureSensitivity == expectedValue)
        verify(mockPenViewContract).setPen(any(NeoPen::class.java), any(Paint::class.java))
    }

    @Test
    fun testPenStateInitialization() {
        // Then
        assert(penViewModel.penState.currentPenType == PenType.Fountain)
        assert(penViewModel.penState.isLogging == false)
        assert(penViewModel.penWidth.get() == penConfig.width.toInt())
    }

    @Test
    fun testViewModelWithoutContractDoesNotCrash() {
        // Given
        val viewModelWithoutContract = PenViewModel(mockContext, NeoPenConfig())
        
        // When & Then - 这些调用不应该崩溃
        viewModelWithoutContract.clear()
        viewModelWithoutContract.repaint()
        viewModelWithoutContract.saveLoggerData()
        viewModelWithoutContract.toggleLogging()
        viewModelWithoutContract.useBallpointPen()
    }
}
