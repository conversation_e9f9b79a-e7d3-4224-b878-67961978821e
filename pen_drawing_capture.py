#!/usr/bin/env python3
"""
完整的笔画绘制捕获和重现工具
集成日志监听、数据解析和位图渲染功能
"""

import os
import sys
import time
import argparse
import subprocess
import json
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from LogcatDrawingCapture import DrawingDataCapture
    from DrawingRenderer import DrawingRenderer
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Please ensure LogcatDrawingCapture.py and DrawingRenderer.py are in the same directory")
    sys.exit(1)

class PenDrawingCaptureManager:
    def __init__(self):
        self.capture_tool = None
        self.renderer = None
        
    def check_dependencies(self):
        """检查依赖项"""
        print("Checking dependencies...")
        
        # 检查adb
        try:
            result = subprocess.run(['adb', 'version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print("✓ ADB is available")
            else:
                print("✗ ADB is not working properly")
                return False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("✗ ADB not found. Please install Android SDK and add adb to PATH")
            return False
        
        # 检查设备连接
        try:
            result = subprocess.run(['adb', 'devices'], 
                                  capture_output=True, text=True, timeout=5)
            devices = [line for line in result.stdout.split('\n') 
                      if line.strip() and not line.startswith('List of devices')]
            
            if devices:
                print(f"✓ Found {len(devices)} connected device(s)")
                for device in devices:
                    print(f"  - {device}")
            else:
                print("✗ No devices connected")
                return False
        except subprocess.TimeoutExpired:
            print("✗ Timeout checking devices")
            return False
        
        # 检查PIL
        try:
            from PIL import Image, ImageDraw
            print("✓ PIL (Pillow) is available")
        except ImportError:
            print("✗ PIL (Pillow) not found. Install with: pip install Pillow")
            return False
        
        return True
    
    def install_and_launch_app(self):
        """安装并启动应用"""
        print("Installing and launching pen demo app...")
        
        # 检查是否在项目目录中
        if not os.path.exists('gradlew'):
            print("Error: Not in project directory. Please run from the pendemo project root.")
            return False
        
        try:
            # 构建并安装应用
            print("Building and installing app...")
            subprocess.run(['./gradlew', 'installDebug'], check=True, timeout=120)
            
            # 启动应用
            print("Launching app...")
            subprocess.run([
                'adb', 'shell', 'am', 'start', 
                '-n', 'com.onyx.demo.pen/.PenActivity'
            ], check=True, timeout=10)
            
            print("✓ App installed and launched successfully")
            print("Please start drawing on the device to capture pen data")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"✗ Error installing/launching app: {e}")
            return False
        except subprocess.TimeoutExpired:
            print("✗ Timeout during app installation/launch")
            return False
    
    def capture_drawing_data(self, output_file: str = None, duration: int = None):
        """捕获绘制数据"""
        if output_file is None:
            timestamp = int(time.time())
            output_file = f"pen_drawing_{timestamp}.json"
        
        print(f"Starting drawing data capture...")
        print(f"Output file: {output_file}")
        
        if duration:
            print(f"Capture duration: {duration} seconds")
        else:
            print("Press Ctrl+C to stop capturing")
        
        self.capture_tool = DrawingDataCapture(output_file)
        
        if duration:
            # 定时捕获
            import threading
            
            def stop_after_duration():
                time.sleep(duration)
                if self.capture_tool.is_capturing:
                    self.capture_tool.stop_capture()
            
            timer = threading.Thread(target=stop_after_duration)
            timer.daemon = True
            timer.start()
        
        try:
            self.capture_tool.start_capture()
            return output_file
        except KeyboardInterrupt:
            print("\nCapture interrupted by user")
            return output_file
    
    def render_captured_data(self, input_file: str, output_file: str = None,
                           canvas_width: int = 1600, canvas_height: int = 2560,
                           create_analysis: bool = False, render_individual: bool = False,
                           create_grid: bool = False, grid_type: str = "both", output_dir: str = None):
        """渲染捕获的数据"""
        if not os.path.exists(input_file):
            print(f"Error: Input file not found: {input_file}")
            return None

        print(f"Rendering captured data from: {input_file}")

        self.renderer = DrawingRenderer(canvas_width, canvas_height)

        try:
            if render_individual:
                # 渲染每个stroke单独的图像
                output_files = self.renderer.render_individual_strokes(input_file, output_dir)
                print(f"Rendered {len(output_files)} individual stroke images")
                return output_files

            elif create_grid:
                # 创建stroke网格分析图
                if output_file is None:
                    base_name = os.path.splitext(input_file)[0]
                    output_file = f"{base_name}_stroke_grid_{grid_type}.png"

                return self.renderer.create_stroke_analysis_grid(input_file, output_file, grid_type)

            elif create_analysis:
                # 创建分析图像
                if output_file is None:
                    base_name = os.path.splitext(input_file)[0]
                    output_file = f"{base_name}_analysis.png"

                return self.renderer.create_analysis_image(
                    json.load(open(input_file, 'r')), output_file
                )
            else:
                # 标准渲染
                return self.renderer.render_from_file(input_file, output_file)

        except Exception as e:
            print(f"Error rendering data: {e}")
            return None
    
    def run_complete_workflow(self, capture_duration: int = 30,
                            canvas_width: int = 1600, canvas_height: int = 2560):
        """运行完整的工作流程"""
        print("=== Pen Drawing Capture Complete Workflow ===")
        print(f"Capture duration: {capture_duration} seconds")
        print(f"Canvas size: {canvas_width}x{canvas_height} (Fixed: 1600x2560)")
        print()
        
        # 1. 检查依赖
        if not self.check_dependencies():
            print("Dependency check failed. Please fix the issues and try again.")
            return False
        
        print()
        
        # 2. 安装并启动应用
        if not self.install_and_launch_app():
            print("Failed to install/launch app.")
            return False
        
        print()
        print("Waiting 5 seconds for app to fully load...")
        time.sleep(5)
        
        # 3. 捕获绘制数据
        timestamp = int(time.time())
        capture_file = f"pen_drawing_{timestamp}.json"
        
        print()
        captured_file = self.capture_drawing_data(capture_file, capture_duration)
        
        if not captured_file or not os.path.exists(captured_file):
            print("No data captured.")
            return False
        
        print()
        
        # 4. 渲染数据
        base_name = os.path.splitext(captured_file)[0]

        # 渲染普通图像
        normal_output = f"{base_name}_rendered.png"
        rendered_file = self.render_captured_data(
            captured_file, normal_output, canvas_width, canvas_height
        )

        # 渲染分析图像
        analysis_output = f"{base_name}_analysis.png"
        analysis_file = self.render_captured_data(
            captured_file, analysis_output, canvas_width, canvas_height,
            create_analysis=True
        )

        # 渲染每个stroke的单独和累积图像
        individual_dir = f"{base_name}_strokes"
        individual_files = self.render_captured_data(
            captured_file, canvas_width=canvas_width, canvas_height=canvas_height,
            render_individual=True, output_dir=individual_dir
        )

        # 创建stroke网格分析图（包含单独和累积两种类型）
        grid_output = f"{base_name}_stroke_grid_both.png"
        grid_file = self.render_captured_data(
            captured_file, grid_output, canvas_width, canvas_height,
            create_grid=True, grid_type="both"
        )
        
        print()
        print("=== Workflow Complete ===")
        print(f"Captured data: {captured_file}")
        if rendered_file:
            print(f"Rendered image: {rendered_file}")
        if analysis_file:
            print(f"Analysis image: {analysis_file}")
        if individual_files:
            single_count = len([f for f in individual_files if "_single.png" in f])
            cumulative_count = len([f for f in individual_files if "_cumulative.png" in f])
            print(f"Individual strokes: {single_count} single + {cumulative_count} cumulative images in {individual_dir}/")
        if grid_file:
            print(f"Stroke grid: {grid_file}")

        return True

def main():
    parser = argparse.ArgumentParser(
        description='Complete pen drawing capture and rendering tool',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run complete workflow (capture for 30 seconds, then render)
  python pen_drawing_capture.py workflow
  
  # Just capture data
  python pen_drawing_capture.py capture -o my_drawing.json -d 60
  
  # Just render existing data
  python pen_drawing_capture.py render my_drawing.json -o output.png
  
  # Check dependencies
  python pen_drawing_capture.py check
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Workflow command
    workflow_parser = subparsers.add_parser('workflow', help='Run complete workflow')
    workflow_parser.add_argument('-d', '--duration', type=int, default=30,
                               help='Capture duration in seconds (default: 30)')
    workflow_parser.add_argument('-w', '--width', type=int, default=1600,
                               help='Canvas width (default: 1600, fixed)')
    workflow_parser.add_argument('--height', type=int, default=2560,
                               help='Canvas height (default: 2560, fixed)')
    
    # Capture command
    capture_parser = subparsers.add_parser('capture', help='Capture drawing data only')
    capture_parser.add_argument('-o', '--output', help='Output JSON file')
    capture_parser.add_argument('-d', '--duration', type=int,
                              help='Capture duration in seconds (default: manual stop)')
    
    # Render command
    render_parser = subparsers.add_parser('render', help='Render captured data only')
    render_parser.add_argument('input_file', help='Input JSON file')
    render_parser.add_argument('-o', '--output', help='Output PNG file')
    render_parser.add_argument('-w', '--width', type=int, default=1600,
                             help='Canvas width (default: 1600, fixed)')
    render_parser.add_argument('--height', type=int, default=2560,
                             help='Canvas height (default: 2560, fixed)')
    render_parser.add_argument('--analysis', action='store_true',
                             help='Create analysis image')
    render_parser.add_argument('--individual', action='store_true',
                             help='Render each stroke individually (both single and cumulative)')
    render_parser.add_argument('--grid', action='store_true',
                             help='Create stroke analysis grid')
    render_parser.add_argument('--grid-type', choices=['single', 'cumulative', 'both'], default='both',
                             help='Grid type: single strokes, cumulative strokes, or both (default: both)')
    render_parser.add_argument('--output-dir', help='Output directory for individual strokes')
    
    # Check command
    check_parser = subparsers.add_parser('check', help='Check dependencies')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    manager = PenDrawingCaptureManager()
    
    if args.command == 'check':
        success = manager.check_dependencies()
        return 0 if success else 1
    
    elif args.command == 'workflow':
        success = manager.run_complete_workflow(
            args.duration, args.width, args.height
        )
        return 0 if success else 1
    
    elif args.command == 'capture':
        captured_file = manager.capture_drawing_data(args.output, args.duration)
        return 0 if captured_file else 1
    
    elif args.command == 'render':
        rendered_file = manager.render_captured_data(
            args.input_file, args.output, args.width, args.height,
            args.analysis, args.individual, args.grid,
            getattr(args, 'grid_type', 'both'), args.output_dir
        )
        return 0 if rendered_file else 1
    
    return 0

if __name__ == '__main__':
    exit(main())
