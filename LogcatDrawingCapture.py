#!/usr/bin/env python3
"""
ADB Logcat绘制数据捕获工具
监听PenDrawLog标签的日志，解析绘制数据并保存
"""

import subprocess
import re
import json
import time
import threading
from datetime import datetime
from typing import List, Dict, Any, Optional
import argparse

class DrawingDataCapture:
    def __init__(self, output_file: str = None):
        self.output_file = output_file or f"drawing_data_{int(time.time())}.json"
        self.strokes = []
        self.current_stroke = None
        self.is_capturing = False
        self.logcat_process = None
        
        # 正则表达式模式
        self.draw_line_pattern = re.compile(
            r'DRAW_LINE\|strokeId=(\d+)\|pointId=(\d+)\|'
            r'startX=([\d.-]+)\|startY=([\d.-]+)\|stopX=([\d.-]+)\|stopY=([\d.-]+)\|'
            r'strokeWidth=([\d.-]+)\|color=(-?\d+)\|alpha=(\d+)\|timestamp=(\d+)'
        )
        
        self.stroke_start_pattern = re.compile(
            r'STROKE_START\|strokeId=(\d+)\|timestamp=(\d+)'
        )
        
        self.draw_path_pattern = re.compile(
            r'DRAW_PATH\|strokeId=(\d+)\|'
            r'strokeWidth=([\d.-]+)\|color=(-?\d+)\|alpha=(\d+)\|timestamp=(\d+)'
        )
        
        self.draw_bitmap_pattern = re.compile(
            r'DRAW_BITMAP\|left=([\d.-]+)\|top=([\d.-]+)\|width=(\d+)\|height=(\d+)'
        )
    
    def start_capture(self):
        """开始捕获logcat数据"""
        if self.is_capturing:
            print("Already capturing...")
            return
        
        print(f"Starting logcat capture, output will be saved to: {self.output_file}")
        print("Press Ctrl+C to stop capturing")
        
        try:
            # 清除现有的logcat缓冲区
            subprocess.run(['adb', 'logcat', '-c'], check=True)
            
            # 启动logcat进程，只监听PenDrawLog标签
            self.logcat_process = subprocess.Popen(
                ['adb', 'logcat', '-s', 'PenDrawLog:D'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1
            )
            
            self.is_capturing = True
            
            # 在单独线程中处理logcat输出
            capture_thread = threading.Thread(target=self._process_logcat_output)
            capture_thread.daemon = True
            capture_thread.start()
            
            # 主线程等待用户中断
            try:
                while self.is_capturing:
                    time.sleep(0.1)
            except KeyboardInterrupt:
                self.stop_capture()
                
        except subprocess.CalledProcessError as e:
            print(f"Error starting adb logcat: {e}")
        except FileNotFoundError:
            print("Error: adb command not found. Please ensure Android SDK is installed and adb is in PATH.")
    
    def stop_capture(self):
        """停止捕获"""
        print("\nStopping capture...")
        self.is_capturing = False
        
        if self.logcat_process:
            self.logcat_process.terminate()
            self.logcat_process.wait()
        
        self._save_data()
        print(f"Capture stopped. Data saved to: {self.output_file}")
    
    def _process_logcat_output(self):
        """处理logcat输出"""
        try:
            for line in iter(self.logcat_process.stdout.readline, ''):
                if not self.is_capturing:
                    break
                
                line = line.strip()
                if not line:
                    continue
                
                # 解析不同类型的日志
                self._parse_log_line(line)
                
        except Exception as e:
            print(f"Error processing logcat output: {e}")
    
    def _parse_log_line(self, line: str):
        """解析单行日志"""
        try:
            # 解析笔画开始
            match = self.stroke_start_pattern.search(line)
            if match:
                stroke_id = int(match.group(1))
                timestamp = int(match.group(2))
                self._start_new_stroke(stroke_id, timestamp)
                return
            
            # 解析绘制线段
            match = self.draw_line_pattern.search(line)
            if match:
                self._parse_draw_line(match)
                return
            
            # 解析绘制路径
            match = self.draw_path_pattern.search(line)
            if match:
                self._parse_draw_path(match)
                return
            
            # 解析位图绘制
            match = self.draw_bitmap_pattern.search(line)
            if match:
                self._parse_draw_bitmap(match)
                return
                
        except Exception as e:
            print(f"Error parsing line: {line}, error: {e}")
    
    def _start_new_stroke(self, stroke_id: int, timestamp: int):
        """开始新的笔画"""
        # 保存之前的笔画（如果存在且有线段）
        if self.current_stroke and self.current_stroke['lines']:
            self.strokes.append(self.current_stroke)
            print(f"Saved stroke {self.current_stroke['stroke_id']} with {len(self.current_stroke['lines'])} lines")

        # 创建新的笔画
        self.current_stroke = {
            'stroke_id': stroke_id,
            'start_timestamp': timestamp,
            'type': 'stroke',
            'lines': [],
            'paths': []
        }
        print(f"Started stroke {stroke_id} at {timestamp}")
    
    def _parse_draw_line(self, match):
        """解析绘制线段"""
        stroke_id = int(match.group(1))
        point_id = int(match.group(2))
        start_x = float(match.group(3))
        start_y = float(match.group(4))
        stop_x = float(match.group(5))
        stop_y = float(match.group(6))
        stroke_width = float(match.group(7))
        color = int(match.group(8))
        alpha = int(match.group(9))
        timestamp = int(match.group(10))
        
        line_data = {
            'point_id': point_id,
            'start_x': start_x,
            'start_y': start_y,
            'stop_x': stop_x,
            'stop_y': stop_y,
            'stroke_width': stroke_width,
            'color': color,
            'alpha': alpha,
            'timestamp': timestamp
        }
        
        # 确保当前笔画存在
        if self.current_stroke is None or self.current_stroke['stroke_id'] != stroke_id:
            self._start_new_stroke(stroke_id, timestamp)
        
        self.current_stroke['lines'].append(line_data)
        
        # 如果这是笔画的第一个点，也记录结束时间
        if point_id == 1:
            self.current_stroke['end_timestamp'] = timestamp
        else:
            self.current_stroke['end_timestamp'] = timestamp
        
        print(f"Line: stroke={stroke_id}, point={point_id}, "
              f"({start_x:.1f},{start_y:.1f})->({stop_x:.1f},{stop_y:.1f}), "
              f"width={stroke_width:.1f}")
    
    def _parse_draw_path(self, match):
        """解析绘制路径"""
        stroke_id = int(match.group(1))
        stroke_width = float(match.group(2))
        color = int(match.group(3))
        alpha = int(match.group(4))
        timestamp = int(match.group(5))
        
        path_data = {
            'stroke_width': stroke_width,
            'color': color,
            'alpha': alpha,
            'timestamp': timestamp
        }
        
        if self.current_stroke is None or self.current_stroke['stroke_id'] != stroke_id:
            self._start_new_stroke(stroke_id, timestamp)
        
        self.current_stroke['paths'].append(path_data)
        print(f"Path: stroke={stroke_id}, width={stroke_width:.1f}")
    
    def _parse_draw_bitmap(self, match):
        """解析位图绘制"""
        left = float(match.group(1))
        top = float(match.group(2))
        width = int(match.group(3))
        height = int(match.group(4))
        
        bitmap_data = {
            'type': 'bitmap',
            'left': left,
            'top': top,
            'width': width,
            'height': height,
            'timestamp': int(time.time() * 1000)
        }
        
        self.strokes.append(bitmap_data)
        print(f"Bitmap: ({left:.1f},{top:.1f}) {width}x{height}")
    
    def _save_data(self):
        """保存捕获的数据"""
        # 添加当前笔画到列表（如果存在且有线段）
        if self.current_stroke and self.current_stroke['lines']:
            self.strokes.append(self.current_stroke)

        # 按stroke_id排序所有笔画
        stroke_data = [s for s in self.strokes if s.get('type') == 'stroke']
        stroke_data.sort(key=lambda x: x.get('stroke_id', 0))

        # 重新整理strokes列表，保持bitmap数据
        bitmap_data = [s for s in self.strokes if s.get('type') == 'bitmap']
        all_strokes = stroke_data + bitmap_data

        # 创建输出数据结构
        output_data = {
            'capture_info': {
                'timestamp': datetime.now().isoformat(),
                'total_strokes': len(stroke_data),
                'total_lines': sum(len(s.get('lines', [])) for s in stroke_data),
                'total_bitmaps': len(bitmap_data),
                'stroke_ids': [s.get('stroke_id') for s in stroke_data]
            },
            'strokes': all_strokes
        }
        
        # 保存到JSON文件
        try:
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            
            print(f"\nCapture Summary:")
            print(f"- Total strokes: {output_data['capture_info']['total_strokes']}")
            print(f"- Total lines: {output_data['capture_info']['total_lines']}")
            print(f"- Total bitmaps: {output_data['capture_info']['total_bitmaps']}")
            
        except Exception as e:
            print(f"Error saving data: {e}")

def main():
    parser = argparse.ArgumentParser(description='Capture drawing data from Android logcat')
    parser.add_argument('-o', '--output', help='Output JSON file name', 
                       default=f"drawing_data_{int(time.time())}.json")
    parser.add_argument('--test', action='store_true', help='Run in test mode (parse sample data)')
    
    args = parser.parse_args()
    
    if args.test:
        print("Test mode - parsing sample log data...")
        # 这里可以添加测试代码
        return
    
    capture = DrawingDataCapture(args.output)
    capture.start_capture()

if __name__ == '__main__':
    main()
