// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id 'com.android.application' version '8.1.2' apply false
    id 'com.android.library' version '8.1.2' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.0' apply false
}

ext {
    // Sdk and tools
    minSdkVersion = 28
    targetSdkVersion = 34
    compileSdkVersion = 34
    gradleVersion = "8.1.2"

    kotlin_version = '1.9.0'
    junitVersion = '4.13.2'
    androidxJUnitVersion = '1.1.3'
    espressoVersion = '3.4.0'
}