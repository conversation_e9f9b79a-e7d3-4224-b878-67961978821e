#!/usr/bin/env python3
"""
测试每个stroke单独渲染功能
"""

import json
import os
import sys
from DrawingRenderer import DrawingRenderer

def create_multi_stroke_test_data():
    """创建包含多个stroke的测试数据"""
    test_data = {
        "capture_info": {
            "timestamp": "2024-07-23T18:30:00.000000",
            "total_strokes": 3,
            "total_lines": 12,
            "total_bitmaps": 0,
            "stroke_ids": [1, 2, 3]
        },
        "strokes": [
            {
                "stroke_id": 1,
                "start_timestamp": 1690123456789,
                "end_timestamp": 1690123457000,
                "type": "stroke",
                "lines": [
                    {
                        "point_id": 1,
                        "start_x": 200.0,
                        "start_y": 200.0,
                        "stop_x": 300.0,
                        "stop_y": 200.0,
                        "stroke_width": 5.0,
                        "color": -16777216,  # 黑色
                        "alpha": 255,
                        "timestamp": 1690123456789
                    },
                    {
                        "point_id": 2,
                        "start_x": 300.0,
                        "start_y": 200.0,
                        "stop_x": 400.0,
                        "stop_y": 300.0,
                        "stroke_width": 5.0,
                        "color": -16777216,
                        "alpha": 255,
                        "timestamp": 1690123456800
                    },
                    {
                        "point_id": 3,
                        "start_x": 400.0,
                        "start_y": 300.0,
                        "stop_x": 500.0,
                        "stop_y": 200.0,
                        "stroke_width": 5.0,
                        "color": -16777216,
                        "alpha": 255,
                        "timestamp": 1690123456900
                    }
                ],
                "paths": []
            },
            {
                "stroke_id": 2,
                "start_timestamp": 1690123458000,
                "end_timestamp": 1690123459000,
                "type": "stroke",
                "lines": [
                    {
                        "point_id": 1,
                        "start_x": 600.0,
                        "start_y": 600.0,
                        "stop_x": 700.0,
                        "stop_y": 600.0,
                        "stroke_width": 8.0,
                        "color": -65536,  # 红色
                        "alpha": 255,
                        "timestamp": 1690123458000
                    },
                    {
                        "point_id": 2,
                        "start_x": 700.0,
                        "start_y": 600.0,
                        "stop_x": 750.0,
                        "stop_y": 650.0,
                        "stroke_width": 8.0,
                        "color": -65536,
                        "alpha": 255,
                        "timestamp": 1690123458100
                    },
                    {
                        "point_id": 3,
                        "start_x": 750.0,
                        "start_y": 650.0,
                        "stop_x": 800.0,
                        "stop_y": 700.0,
                        "stroke_width": 8.0,
                        "color": -65536,
                        "alpha": 255,
                        "timestamp": 1690123458200
                    },
                    {
                        "point_id": 4,
                        "start_x": 800.0,
                        "start_y": 700.0,
                        "stop_x": 850.0,
                        "stop_y": 650.0,
                        "stroke_width": 8.0,
                        "color": -65536,
                        "alpha": 255,
                        "timestamp": 1690123458300
                    }
                ],
                "paths": []
            },
            {
                "stroke_id": 3,
                "start_timestamp": 1690123460000,
                "end_timestamp": 1690123461000,
                "type": "stroke",
                "lines": [
                    {
                        "point_id": 1,
                        "start_x": 1000.0,
                        "start_y": 1000.0,
                        "stop_x": 1100.0,
                        "stop_y": 1000.0,
                        "stroke_width": 3.0,
                        "color": -16711936,  # 绿色
                        "alpha": 255,
                        "timestamp": 1690123460000
                    },
                    {
                        "point_id": 2,
                        "start_x": 1100.0,
                        "start_y": 1000.0,
                        "stop_x": 1150.0,
                        "stop_y": 1050.0,
                        "stroke_width": 3.0,
                        "color": -16711936,
                        "alpha": 255,
                        "timestamp": 1690123460100
                    },
                    {
                        "point_id": 3,
                        "start_x": 1150.0,
                        "start_y": 1050.0,
                        "stop_x": 1200.0,
                        "stop_y": 1100.0,
                        "stroke_width": 3.0,
                        "color": -16711936,
                        "alpha": 255,
                        "timestamp": 1690123460200
                    },
                    {
                        "point_id": 4,
                        "start_x": 1200.0,
                        "start_y": 1100.0,
                        "stop_x": 1250.0,
                        "stop_y": 1150.0,
                        "stroke_width": 3.0,
                        "color": -16711936,
                        "alpha": 255,
                        "timestamp": 1690123460300
                    },
                    {
                        "point_id": 5,
                        "start_x": 1250.0,
                        "start_y": 1150.0,
                        "stop_x": 1300.0,
                        "stop_y": 1200.0,
                        "stroke_width": 3.0,
                        "color": -16711936,
                        "alpha": 255,
                        "timestamp": 1690123460400
                    }
                ],
                "paths": []
            }
        ]
    }
    return test_data

def test_individual_stroke_rendering():
    """测试每个stroke单独渲染功能"""
    print("Testing individual stroke rendering...")
    
    # 创建测试数据
    test_data = create_multi_stroke_test_data()
    test_file = "test_multi_strokes.json"
    
    # 保存测试数据
    with open(test_file, 'w') as f:
        json.dump(test_data, f, indent=2)
    
    print(f"Created test data file: {test_file}")
    print(f"Test data contains {test_data['capture_info']['total_strokes']} strokes")
    
    # 创建渲染器
    renderer = DrawingRenderer()
    
    # 1. 测试标准渲染（所有stroke在一张图上）
    print("\n1. Testing standard rendering...")
    standard_output = "test_all_strokes.png"
    renderer.render_from_file(test_file, standard_output)
    print(f"✓ Standard rendering: {standard_output}")
    
    # 2. 测试每个stroke单独渲染
    print("\n2. Testing individual stroke rendering...")
    individual_dir = "test_individual_strokes"
    individual_files = renderer.render_individual_strokes(test_file, individual_dir)
    print(f"✓ Individual stroke rendering: {len(individual_files)} files in {individual_dir}/")
    for file in individual_files:
        print(f"  - {file}")
    
    # 3. 测试stroke网格分析图
    print("\n3. Testing stroke grid analysis...")
    grid_output = "test_stroke_grid.png"
    renderer.create_stroke_analysis_grid(test_file, grid_output)
    print(f"✓ Stroke grid analysis: {grid_output}")
    
    # 4. 验证文件存在
    print("\n4. Verifying output files...")
    all_files = [standard_output, grid_output] + individual_files
    
    for file in all_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✓ {file} ({size} bytes)")
        else:
            print(f"✗ {file} (missing)")
            return False
    
    print(f"\n✓ All tests passed! Generated {len(all_files)} files total.")
    
    # 清理测试文件
    cleanup_files = [test_file, standard_output, grid_output] + individual_files
    if os.path.exists(individual_dir):
        import shutil
        shutil.rmtree(individual_dir)
        print(f"Cleaned up directory: {individual_dir}")
    
    for file in [test_file, standard_output, grid_output]:
        if os.path.exists(file):
            os.remove(file)
            print(f"Cleaned up: {file}")
    
    return True

def test_command_line_interface():
    """测试命令行接口"""
    print("\nTesting command line interface...")
    
    # 创建测试数据
    test_data = create_multi_stroke_test_data()
    test_file = "test_cli_strokes.json"
    
    with open(test_file, 'w') as f:
        json.dump(test_data, f, indent=2)
    
    import subprocess
    
    try:
        # 测试individual参数
        print("Testing --individual flag...")
        result = subprocess.run([
            'python3', 'DrawingRenderer.py', test_file, 
            '--individual', '--output-dir', 'test_cli_individual'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✓ --individual flag works")
        else:
            print(f"✗ --individual flag failed: {result.stderr}")
        
        # 测试grid参数
        print("Testing --grid flag...")
        result = subprocess.run([
            'python3', 'DrawingRenderer.py', test_file, 
            '--grid', '-o', 'test_cli_grid.png'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✓ --grid flag works")
        else:
            print(f"✗ --grid flag failed: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("✗ Command line test timed out")
        return False
    except Exception as e:
        print(f"✗ Command line test error: {e}")
        return False
    finally:
        # 清理
        cleanup_files = [test_file, 'test_cli_grid.png']
        for file in cleanup_files:
            if os.path.exists(file):
                os.remove(file)
        
        if os.path.exists('test_cli_individual'):
            import shutil
            shutil.rmtree('test_cli_individual')
    
    return True

def main():
    """主测试函数"""
    print("=" * 60)
    print("Individual Stroke Rendering Test")
    print("=" * 60)
    
    try:
        # 测试核心功能
        if not test_individual_stroke_rendering():
            return 1
        
        # 测试命令行接口
        if not test_command_line_interface():
            return 1
        
        print("\n" + "=" * 60)
        print("All tests completed successfully!")
        print("Individual stroke rendering is working properly.")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
