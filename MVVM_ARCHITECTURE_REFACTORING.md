# MVVM架构重构文档

## 🎯 重构目标

将PenView和PenViewModel之间的紧耦合关系重构为符合MVVM架构原则的松耦合设计。

## 📊 重构前后对比

### 重构前的问题
```kotlin
// ❌ 违反MVVM原则的设计
class PenViewModel(var penView: PenView, var penConfig: NeoPenConfig) {
    // ViewModel直接依赖具体的View实现
    fun clear() {
        penView.clear() // 直接调用View方法
    }
}
```

### 重构后的设计
```kotlin
// ✅ 符合MVVM原则的设计
class PenViewModel(
    private val context: Context,
    private var penConfig: NeoPenConfig
) : ViewModel() {
    private var penViewContract: PenViewContract? = null
    
    fun clear() {
        penViewContract?.clear() // 通过接口调用
    }
}
```

## 🏗️ 架构组件

### 1. PenViewContract (接口层)
```kotlin
interface PenViewContract {
    fun setPen(pen: NeoPen, paint: Paint)
    fun clear()
    fun repaint()
    fun setPenBackground(bitmap: Bitmap)
    // ... 其他接口方法
}
```

**职责**:
- 定义View层应该提供的功能
- 作为ViewModel和View之间的契约
- 实现依赖倒置原则

### 2. PenState (状态管理)
```kotlin
data class PenState(
    val motionEvent: ObservableField<String> = ObservableField(""),
    val loggingButtonText: ObservableField<String> = ObservableField(""),
    val penWidth: ObservableInt = ObservableInt(10),
    // ... 其他状态属性
)
```

**职责**:
- 集中管理所有可观察的状态
- 提供状态更新方法
- 作为MVVM中的Model层

### 3. PenViewModel (业务逻辑层)
```kotlin
class PenViewModel(
    private val context: Context,
    private var penConfig: NeoPenConfig
) : ViewModel() {
    private var penViewContract: PenViewContract? = null
    val penState = PenState()
    
    fun setPenViewContract(contract: PenViewContract) {
        penViewContract = contract
    }
}
```

**职责**:
- 管理业务逻辑
- 通过接口与View交互
- 管理状态变化
- 不直接依赖具体View实现

### 4. PenView (视图层)
```kotlin
class PenView : View, PenViewContract {
    override fun setPen(pen: NeoPen, paint: Paint) {
        // 实现接口方法
    }
    // ... 其他接口实现
}
```

**职责**:
- 实现PenViewContract接口
- 处理UI渲染和用户交互
- 不包含业务逻辑

### 5. PenViewModelFactory (工厂类)
```kotlin
class PenViewModelFactory(
    private val context: Context,
    private val penConfig: NeoPenConfig
) : ViewModelProvider.Factory {
    // 创建ViewModel实例
}
```

**职责**:
- 管理ViewModel的创建
- 处理依赖注入
- 遵循Android Architecture Components最佳实践

## 🔄 依赖关系图

```
┌─────────────────┐
│   PenActivity   │
└─────────┬───────┘
          │ creates
          ▼
┌─────────────────┐    implements    ┌─────────────────┐
│   PenViewModel  │◄─────────────────│ PenViewContract │
└─────────┬───────┘                  └─────────▲───────┘
          │ uses                               │
          ▼                                    │ implements
┌─────────────────┐                  ┌─────────────────┐
│    PenState     │                  │     PenView     │
└─────────────────┘                  └─────────────────┘
```

## 📝 重构步骤

### 1. 创建接口层
- [x] 创建`PenViewContract`接口
- [x] 定义View层应该提供的所有方法

### 2. 创建状态管理
- [x] 创建`PenState`数据类
- [x] 集中管理所有可观察属性
- [x] 提供状态更新方法

### 3. 重构ViewModel
- [x] 移除对PenView的直接依赖
- [x] 添加PenViewContract接口引用
- [x] 实现依赖注入方法
- [x] 重构所有业务逻辑方法

### 4. 更新View层
- [x] 让PenView实现PenViewContract接口
- [x] 添加override关键字到接口方法
- [x] 确保所有接口方法正确实现

### 5. 更新Activity
- [x] 修改ViewModel创建方式
- [x] 通过依赖注入设置View接口
- [x] 创建ViewModelFactory

### 6. 创建工厂类
- [x] 创建PenViewModelFactory
- [x] 实现ViewModelProvider.Factory接口
- [x] 管理ViewModel创建逻辑

## ✅ 重构收益

### 1. 架构清晰
- **分离关注点**: 每个组件职责明确
- **依赖倒置**: ViewModel依赖接口而非具体实现
- **可测试性**: 可以轻松mock接口进行单元测试

### 2. 可维护性
- **松耦合**: 组件间依赖关系清晰
- **可扩展**: 可以轻松添加新的View实现
- **可重用**: ViewModel可以与不同的View实现配合

### 3. 符合最佳实践
- **MVVM模式**: 严格遵循MVVM架构原则
- **Android Architecture Components**: 使用ViewModel和数据绑定
- **SOLID原则**: 遵循单一职责、开闭原则等

## 🧪 测试策略

### 1. 单元测试
```kotlin
class PenViewModelTest {
    @Test
    fun testClearFunctionality() {
        val mockContract = mock<PenViewContract>()
        val viewModel = PenViewModel(context, config)
        viewModel.setPenViewContract(mockContract)
        
        viewModel.clear()
        
        verify(mockContract).clear()
    }
}
```

### 2. 集成测试
- 测试ViewModel与真实View的交互
- 验证数据绑定的正确性
- 测试状态变化的传播

### 3. UI测试
- 测试用户交互的完整流程
- 验证UI状态的正确更新
- 测试异常情况的处理

## 🚀 使用示例

### 在Activity中使用
```kotlin
class PenActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 使用工厂创建ViewModel
        val factory = PenViewModelFactory(this, NeoPenConfig())
        val viewModel = ViewModelProvider(this, factory)[PenViewModel::class.java]
        
        // 设置数据绑定
        binding.penModel = viewModel
        
        // 依赖注入
        viewModel.setPenViewContract(binding.penView)
    }
}
```

### 在测试中使用
```kotlin
class PenViewModelTest {
    private lateinit var viewModel: PenViewModel
    private lateinit var mockContract: PenViewContract
    
    @Before
    fun setup() {
        mockContract = mock()
        viewModel = PenViewModel(context, NeoPenConfig())
        viewModel.setPenViewContract(mockContract)
    }
    
    @Test
    fun testPenTypeChange() {
        viewModel.useBallpointPen()
        
        verify(mockContract).setPen(any(), any())
        assertEquals(PenType.Ballpoint, viewModel.getCurrentPenType())
    }
}
```

## 📋 总结

通过这次重构，我们成功地：

1. **消除了ViewModel对View的直接依赖**
2. **建立了清晰的架构层次**
3. **提高了代码的可测试性和可维护性**
4. **遵循了MVVM架构的最佳实践**

重构后的代码更加符合Android开发的现代化标准，为后续的功能扩展和维护奠定了良好的基础。
