# 笔画绘制捕获和重现工具使用指南

## 概述

本工具集提供了完整的Android手写笔画捕获、解析和重现功能。通过监听adb logcat中的绘制日志，解析坐标和线宽数据，并将其重新绘制为位图文件保存。

## 工具组成

### 1. 核心文件
- `LoggingCanvas.kt` - Canvas包装器，记录所有绘制操作到logcat
- `LogcatDrawingCapture.py` - 日志监听工具，实时捕获绘制数据
- `DrawingRenderer.py` - 数据渲染器，将捕获的数据重新绘制为位图
- `pen_drawing_capture.py` - 完整工具集成脚本

### 2. 功能特点
- **实时监听**：通过adb logcat实时捕获绘制日志
- **数据解析**：解析坐标、线宽、颜色、透明度等绘制参数
- **位图重现**：根据捕获的数据重新绘制笔画
- **性能分析**：提供绘制统计和分析信息

## 安装和配置

### 1. 依赖项检查
```bash
# 检查所有依赖项
python3 pen_drawing_capture.py check
```

需要的依赖：
- Android SDK (adb命令)
- Python 3.x
- PIL/Pillow库 (`pip install Pillow`)
- 连接的Android设备

### 2. 应用构建和安装
```bash
# 在项目根目录执行
./gradlew installDebug

# 启动应用
adb shell am start -n com.onyx.demo.pen/.PenActivity
```

## 使用方法

### 1. 完整工作流程（推荐）
```bash
# 运行完整工作流程：安装应用 -> 捕获30秒 -> 渲染图像
# 默认画布尺寸：1600x2560（适合现代Android设备）
python3 pen_drawing_capture.py workflow

# 自定义参数（保持固定尺寸1600x2560）
python3 pen_drawing_capture.py workflow -d 60
```

### 2. 分步操作

#### 步骤1：捕获绘制数据
```bash
# 手动停止捕获（按Ctrl+C）
python3 pen_drawing_capture.py capture -o my_drawing.json

# 定时捕获（60秒）
python3 pen_drawing_capture.py capture -o my_drawing.json -d 60
```

#### 步骤2：渲染位图
```bash
# 基本渲染
python3 pen_drawing_capture.py render my_drawing.json -o output.png

# 创建分析图像（固定尺寸1600x2560）
python3 pen_drawing_capture.py render my_drawing.json --analysis -o analysis.png

# 画布尺寸已固定为1600x2560，适合现代Android设备
python3 pen_drawing_capture.py render my_drawing.json -o output.png
```

### 3. 直接使用单个工具

#### 使用LogcatDrawingCapture.py
```bash
# 基本捕获
python3 LogcatDrawingCapture.py -o drawing_data.json

# 测试模式
python3 LogcatDrawingCapture.py --test
```

#### 使用DrawingRenderer.py
```bash
# 基本渲染
python3 DrawingRenderer.py drawing_data.json -o rendered.png

# 创建分析图像（固定尺寸1600x2560）
python3 DrawingRenderer.py drawing_data.json --analysis
```

## 数据格式

### 捕获的JSON数据结构
```json
{
  "capture_info": {
    "timestamp": "2024-07-23T17:59:54.123456",
    "total_strokes": 5,
    "total_lines": 150,
    "total_bitmaps": 1
  },
  "strokes": [
    {
      "stroke_id": 1,
      "start_timestamp": 1690123456789,
      "end_timestamp": 1690123457890,
      "type": "stroke",
      "lines": [
        {
          "point_id": 1,
          "start_x": 100.5,
          "start_y": 200.3,
          "stop_x": 102.1,
          "stop_y": 205.7,
          "stroke_width": 3.0,
          "color": -16777216,
          "alpha": 255,
          "timestamp": 1690123456789
        }
      ],
      "paths": []
    }
  ]
}
```

### 日志格式
LoggingCanvas输出的日志格式：
```
D PenDrawLog: STROKE_START|strokeId=1|timestamp=1690123456789
D PenDrawLog: DRAW_LINE|strokeId=1|pointId=1|startX=100.5|startY=200.3|stopX=102.1|stopY=205.7|strokeWidth=3.0|color=-16777216|alpha=255|timestamp=1690123456789
D PenDrawLog: DRAW_PATH|strokeId=2|strokeWidth=5.0|color=-65536|alpha=128|timestamp=1690123457000
D PenDrawLog: DRAW_BITMAP|left=0.0|top=0.0|width=800|height=1200
```

## 高级功能

### 1. 性能监控
应用集成了性能监控功能：
```kotlin
// 启动性能监控
penView.startPerformanceMonitoring()

// 获取性能统计
val stats = penView.getPerformanceStats()
```

### 2. 固定画布尺寸
画布尺寸已固定为1600x2560，这是基于以下考虑：
- 适合现代Android设备的高分辨率屏幕
- 保持16:25.6的宽高比，接近常见的手机屏幕比例
- 提供足够的分辨率用于详细的笔画捕获

```bash
# 获取设备实际屏幕尺寸（参考用）
adb shell wm size

# 工具使用固定的1600x2560画布尺寸
python3 pen_drawing_capture.py workflow
```

### 3. 批量处理
```bash
# 批量渲染多个JSON文件
for file in *.json; do
    python3 DrawingRenderer.py "$file" -o "${file%.json}.png"
done
```

## 故障排除

### 1. 没有捕获到数据
- 确保应用正在运行并且有绘制操作
- 检查logcat权限：`adb logcat -s "PenDrawLog:D"`
- 确认LoggingCanvas正确集成

### 2. 渲染图像为空
- 检查JSON文件是否包含有效数据
- 验证坐标范围是否在画布内
- 调整画布大小参数

### 3. adb连接问题
```bash
# 重启adb服务
adb kill-server
adb start-server

# 检查设备连接
adb devices
```

### 4. Python依赖问题
```bash
# 安装Pillow
pip install Pillow

# 或使用conda
conda install pillow
```

## 示例用法

### 完整示例：捕获并分析绘制数据
```bash
# 1. 检查环境
python3 pen_drawing_capture.py check

# 2. 运行完整工作流程
python3 pen_drawing_capture.py workflow -d 45

# 3. 查看生成的文件
ls -la pen_drawing_*.json pen_drawing_*.png

# 4. 创建分析图（固定1600x2560尺寸）
python3 pen_drawing_capture.py render pen_drawing_*.json --analysis -o analysis.png
```

### 实时监控示例
```bash
# 终端1：启动实时捕获
python3 LogcatDrawingCapture.py -o realtime_capture.json

# 终端2：在设备上绘制

# 终端3：实时查看日志
adb logcat -s "PenDrawLog:D"
```

## 输出文件说明

- `*.json` - 捕获的原始绘制数据
- `*_rendered.png` - 基本渲染图像
- `*_analysis.png` - 包含统计信息的分析图像

分析图像包含：
- 原始绘制内容
- 笔画统计信息
- 绘制参数详情
- 画布尺寸信息

## 技术细节

### LoggingCanvas工作原理
1. 包装原始Canvas对象
2. 拦截所有绘制方法调用
3. 记录绘制参数到logcat
4. 执行实际绘制操作

### 数据流程
```
触摸事件 -> PenView -> LoggingCanvas -> logcat -> Python捕获 -> JSON数据 -> PIL渲染 -> PNG图像
```

### 坐标系统
- Android坐标系：左上角为原点(0,0)，向右向下为正方向
- PIL坐标系：与Android坐标系一致
- 坐标单位：像素(px)

这个工具集为Android手写应用的开发和调试提供了强大的数据捕获和分析能力。
